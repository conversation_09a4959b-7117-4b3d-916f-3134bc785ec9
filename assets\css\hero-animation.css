/* Estilos para Hero Section Moderna */
/* Inspirado em startups de tech como Hugging Face */

.hero {
    position: relative;
    min-height: 100vh;
    padding: 200px 0 80px;
    overflow: hidden;
    display: flex;
    align-items: center;
}

/* Correção para a navbar ficar sempre por cima */
.navbar {
    z-index: 1001 !important;
}

/* Fundo e elementos de background */
.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(rgba(45, 127, 249, 0.15) 2px, transparent 2px);
    background-size: 40px 40px;
    animation: particlesFade 10s infinite alternate;
}

.hero-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(6, 1fr);
}

.grid-line {
    position: relative;
    height: 100%;
    border-right: 1px solid rgba(45, 127, 249, 0.06);
}

.grid-line::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(45, 127, 249, 0.03), transparent 15%, transparent 85%, rgba(45, 127, 249, 0.03));
}

.hero-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 20%, rgba(0, 194, 255, 0.1) 0%, transparent 50%);
    animation: gradientMove 15s ease-in-out infinite alternate;
}

/* Conteúdo principal */
.hero .container {
    position: relative;
    z-index: 5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    height: calc(100vh - 300px);
}

.hero-content {
    display: flex;
    flex-direction: column;
    max-width: 550px;
    animation: fadeInUp 1s ease-out;
    position: relative;
    z-index: 6;
}

/* Badge no topo */
.hero-badge {
    display: inline-flex;
    align-items: center;
    background-color: rgba(45, 127, 249, 0.1);
    padding: 8px 12px;
    border-radius: 50px;
    margin-bottom: 24px;
    margin-top: 30px;
    backdrop-filter: blur(8px);
    border: 1px solid rgba(45, 127, 249, 0.2);
    box-shadow: 0 2px 8px rgba(45, 127, 249, 0.08);
    animation: badgePulse 3s infinite alternate;
    max-width: fit-content;
    position: relative;
    z-index: 7;
}

.hero-badge i {
    color: var(--primary-color);
    font-size: 14px;
    margin-right: 8px;
}

.hero-badge span {
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-dark);
}

/* Título com efeito de gradiente */
.hero-title {
    font-size: 54px;
    line-height: 1.15;
    font-weight: 800;
    margin-bottom: 24px;
    margin-top: 10px;
    letter-spacing: -0.5px;
    color: var(--neutral-900);
    position: relative;
}

.gradient-text {
    background: linear-gradient(to right, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    position: relative;
    display: inline-block;
}

.gradient-text::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 6px;
    bottom: 6px;
    left: 0;
    background: linear-gradient(to right, rgba(45, 127, 249, 0.2), rgba(0, 194, 255, 0.2));
    z-index: -1;
    border-radius: 4px;
}

/* Descrição com efeito de digitação */
.hero-description {
    font-size: 20px;
    line-height: 1.6;
    color: var(--neutral-700);
    margin-bottom: 32px;
    max-width: 500px;
}

.typed-text {
    color: var(--primary-color);
    font-weight: 600;
    position: relative;
    min-width: 120px;
    display: inline-block;
}

.typed-text::after {
    content: '|';
    color: var(--primary-color);
    animation: cursorBlink 1s infinite;
}

/* Ícones de tecnologias */
.tech-icons {
    display: flex;
    gap: 16px;
    margin-bottom: 32px;
    flex-wrap: wrap;
}

.tech-icon {
    position: relative;
    width: 44px;
    height: 44px;
    background-color: white;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    cursor: pointer;
    animation: iconFloat 3s infinite alternate;
    animation-delay: calc(var(--i, 0) * 0.2s);
}

.tech-icon:nth-child(1) { --i: 1; }
.tech-icon:nth-child(2) { --i: 2; }
.tech-icon:nth-child(3) { --i: 3; }
.tech-icon:nth-child(4) { --i: 4; }
.tech-icon:nth-child(5) { --i: 5; }
.tech-icon:nth-child(6) { --i: 6; }

.tech-icon i {
    font-size: 20px;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.tech-icon:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 16px rgba(45, 127, 249, 0.15);
    background-color: var(--primary-color);
}

.tech-icon:hover i {
    color: white;
}

.tech-tooltip {
    position: absolute;
    bottom: -36px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--neutral-800);
    color: white;
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    white-space: nowrap;
    z-index: 10;
}

.tech-tooltip::before {
    content: '';
    position: absolute;
    top: -4px;
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
    width: 8px;
    height: 8px;
    background-color: var(--neutral-800);
}

.tech-icon:hover .tech-tooltip {
    opacity: 1;
    visibility: visible;
    bottom: -40px;
}

/* Botões de CTA */
.hero-cta {
    display: flex;
    gap: 16px;
    margin-bottom: 56px;
}

.hero-cta .button {
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.hero-cta .button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transform: skewX(-30deg);
    transition: all 0.5s ease;
}

.hero-cta .button:hover::before {
    left: 100%;
}

/* Scroll indicator */
.hero-scroll {
    display: flex;
    align-items: center;
    gap: 12px;
    opacity: 0.7;
    margin-top: auto;
    animation: fadeInUp 1.5s ease-out 0.5s forwards;
    cursor: pointer;
}

.scroll-icon {
    position: relative;
}

.mouse {
    width: 26px;
    height: 40px;
    border: 2px solid var(--neutral-500);
    border-radius: 20px;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding-top: 8px;
}

.wheel {
    width: 4px;
    height: 8px;
    background-color: var(--primary-color);
    border-radius: 2px;
    animation: scrollWheel 2s infinite;
}

.arrow {
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
}

.arrow span {
    display: block;
    width: 8px;
    height: 8px;
    border-right: 2px solid var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
    transform: rotate(45deg);
    animation: arrowDown 1.5s infinite;
}

.arrow span:nth-child(2) {
    animation-delay: 0.2s;
}

.hero-scroll p {
    font-size: 14px;
    color: var(--neutral-600);
    font-weight: 500;
}

/* Visual elements */
.hero-visual {
    position: relative;
    width: 45%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 4;
}

.hero-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.shape {
    position: absolute;
    border-radius: 50%;
    filter: blur(50px);
    opacity: 0.4;
}

.shape-1 {
    width: 300px;
    height: 300px;
    background-color: rgba(45, 127, 249, 0.2);
    top: 20%;
    right: 10%;
    animation: shapeFloat 8s infinite alternate;
}

.shape-2 {
    width: 200px;
    height: 200px;
    background-color: rgba(0, 194, 255, 0.15);
    bottom: 30%;
    right: 25%;
    animation: shapeFloat 12s infinite alternate-reverse;
}

.shape-3 {
    width: 150px;
    height: 150px;
    background-color: rgba(124, 58, 237, 0.1);
    top: 60%;
    right: 5%;
    animation: shapeFloat 10s infinite alternate;
}

.hero-model {
    position: relative;
    z-index: 2;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.model-container {
    position: relative;
    width: 300px;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    perspective: 1000px;
    transform-style: preserve-3d;
}

.model-animation {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: radial-gradient(circle at center, 
                            rgba(45, 127, 249, 0.2) 0%, 
                            transparent 70%);
    animation: pulse 3s infinite alternate;
}

.model-glow {
    position: absolute;
    width: 70%;
    height: 70%;
    border-radius: 50%;
    background: conic-gradient(
        from 0deg, 
        rgba(45, 127, 249, 0) 0%, 
        rgba(45, 127, 249, 0.2) 25%, 
        rgba(0, 194, 255, 0.2) 50%, 
        rgba(45, 127, 249, 0.2) 75%, 
        rgba(45, 127, 249, 0) 100%
    );
    animation: rotate 10s linear infinite;
}

.model-card {
    position: absolute;
    background: white;
    border-radius: 10px;
    padding: 10px 16px;
    display: flex;
    align-items: center;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.08);
    z-index: 3;
    overflow: hidden;
    transition: all 0.3s ease;
}

.model-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--primary-color), var(--accent-color));
}

.card-1 {
    top: 30%;
    right: -50px;
    transform: translateX(0);
    animation: cardFloat 4s ease-in-out infinite alternate;
}

.card-2 {
    bottom: 30%;
    left: -50px;
    transform: translateX(0);
    animation: cardFloat 4s ease-in-out 2s infinite alternate-reverse;
}

.card-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background-color: rgba(45, 127, 249, 0.1);
    margin-right: 12px;
}

.card-icon i {
    color: var(--primary-color);
    font-size: 16px;
}

.card-info {
    font-size: 14px;
    font-weight: 600;
    color: var(--neutral-800);
}

.card-line {
    position: absolute;
    top: 50%;
    left: -30px;
    width: 30px;
    height: 1px;
    background-color: rgba(45, 127, 249, 0.2);
    z-index: -1;
}

.card-1 .card-line {
    left: auto;
    right: -30px;
}

/* Animações para serviços e outros elementos */
.will-animate {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.7s cubic-bezier(0.25, 0.1, 0.25, 1.0);
}

.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.service-card.will-animate {
    transition-delay: calc(var(--i, 0) * 0.1s);
}

.service-card:nth-child(1) { --i: 1; }
.service-card:nth-child(2) { --i: 2; }
.service-card:nth-child(3) { --i: 3; }

.case-card.will-animate {
    transition-delay: calc(var(--i, 0) * 0.15s);
}

.case-card:nth-child(1) { --i: 1; }
.case-card:nth-child(2) { --i: 2; }

.stat-item.will-animate {
    transition-delay: calc(var(--i, 0) * 0.1s);
}

.stat-item:nth-child(1) { --i: 1; }
.stat-item:nth-child(2) { --i: 2; }
.stat-item:nth-child(3) { --i: 3; }

.step.will-animate {
    transition-delay: calc(var(--i, 0) * 0.15s);
}

.step:nth-child(1) { --i: 1; }
.step:nth-child(2) { --i: 2; }
.step:nth-child(3) { --i: 3; }

/* Animações */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes badgePulse {
    0% {
        box-shadow: 0 0 0 0 rgba(45, 127, 249, 0.2);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(45, 127, 249, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(45, 127, 249, 0);
    }
}

@keyframes particlesFade {
    0% {
        opacity: 0.3;
    }
    100% {
        opacity: 0.7;
    }
}

@keyframes gradientMove {
    0% {
        background-position: 0% 0%;
    }
    100% {
        background-position: 100% 100%;
    }
}

@keyframes cursorBlink {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0;
    }
}

@keyframes iconFloat {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(-8px);
    }
}

@keyframes scrollWheel {
    0% {
        opacity: 1;
        transform: translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateY(10px);
    }
}

@keyframes arrowDown {
    0% {
        opacity: 0;
        transform: rotate(45deg) translate(-5px, -5px);
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0;
        transform: rotate(45deg) translate(5px, 5px);
    }
}

@keyframes shapeFloat {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(-15px, 15px);
    }
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
        opacity: 0.6;
    }
    100% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes cardFloat {
    0% {
        transform: translateY(0px);
    }
    100% {
        transform: translateY(-15px);
    }
}

/* Modelo 3D e elementos visuais aprimorados */
.model-showcase {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: none;
    z-index: 2;
}

.model-platform {
    position: relative;
    width: 180px;
    height: 180px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.platform-ring {
    position: absolute;
    width: 160px;
    height: 160px;
    border: 2px solid rgba(45, 127, 249, 0.3);
    border-radius: 50%;
    animation: rotate 20s linear infinite;
}

.platform-ring::before,
.platform-ring::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 50%;
}

.platform-ring::before {
    width: 180px;
    height: 180px;
    border: 1px dashed rgba(45, 127, 249, 0.2);
    animation: rotate 30s linear infinite reverse;
}

.platform-ring::after {
    width: 140px;
    height: 140px;
    border: 1px solid rgba(0, 194, 255, 0.2);
    animation: pulse 4s ease-in-out infinite alternate;
}

.platform-base {
    position: absolute;
    width: 120px;
    height: 120px;
    background: radial-gradient(
        circle at center,
        rgba(45, 127, 249, 0.1) 0%,
        rgba(45, 127, 249, 0.05) 60%,
        transparent 80%
    );
    border-radius: 50%;
    transform: translateY(60px) rotateX(60deg);
    animation: basePulse 4s ease-in-out infinite alternate;
}

.hologram-effect {
    position: absolute;
    width: 80px;
    height: 80px;
    background-color: rgba(45, 127, 249, 0.05);
    border-radius: 8px;
    transform: rotateX(10deg) rotateY(20deg) rotateZ(5deg);
    box-shadow: 0 0 20px rgba(0, 194, 255, 0.2);
    animation: holoFloat 6s ease-in-out infinite alternate;
}

.hologram-effect::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, rgba(45, 127, 249, 0.08), rgba(0, 194, 255, 0.08));
    border-radius: 12px;
    z-index: -1;
    opacity: 0.7;
    animation: holoPulse 3s ease-in-out infinite alternate;
}

.hologram-scan {
    position: absolute;
    width: 80px;
    height: 2px;
    background: linear-gradient(to right, transparent, rgba(0, 194, 255, 0.8), transparent);
    animation: scanLine 2s ease-in-out infinite;
}

.model-labels {
    position: absolute;
    top: -20px;
    right: -80px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.label-connector {
    width: 40px;
    height: 1px;
    background-color: rgba(45, 127, 249, 0.3);
    margin-bottom: 5px;
}

.label-text {
    font-size: 12px;
    font-weight: 600;
    color: var(--primary-color);
    padding: 4px 8px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    position: relative;
    animation: labelFloat 4s ease-in-out infinite alternate;
}

.label-text::before {
    content: '';
    position: absolute;
    left: -4px;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background-color: var(--primary-color);
    border-radius: 50%;
    opacity: 0.5;
}

/* Métricas flutuantes */
.model-metrics {
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 30px;
    z-index: 3;
}

.metric-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 12px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    animation: metricFloat 5s ease-in-out infinite alternate;
    animation-delay: calc(var(--i, 0) * 0.5s);
    position: relative;
    overflow: hidden;
}

.metric-item:nth-child(1) { --i: 1; }
.metric-item:nth-child(2) { --i: 2; }

.metric-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right, var(--primary-color), var(--accent-color));
}

.metric-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 24px;
    height: 24px;
    margin-bottom: 5px;
    color: var(--primary-color);
}

.metric-value {
    font-size: 18px;
    font-weight: 700;
    color: var(--neutral-900);
    display: flex;
    align-items: baseline;
}

.metric-value span {
    font-size: 12px;
    margin-left: 2px;
    font-weight: 500;
    color: var(--neutral-600);
}

.metric-label {
    font-size: 12px;
    color: var(--neutral-600);
    margin-top: 2px;
}

/* Novo card adicional */
.model-card.card-3 {
    top: 5%;
    right: -40px;
    z-index: 4;
    animation-delay: 0.6s;
}

/* Elementos flutuantes */
.model-floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 1;
    pointer-events: none;
}

.floating-cube {
    position: absolute;
    width: 20px;
    height: 20px;
    background-color: rgba(45, 127, 249, 0.1);
    border: 1px solid rgba(45, 127, 249, 0.2);
    bottom: 30%;
    left: 10%;
    transform-style: preserve-3d;
    animation: floatObject 8s ease-in-out infinite alternate;
}

.floating-cube::before,
.floating-cube::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-cube::before {
    right: -100%;
    bottom: 0;
    transform: rotateY(90deg);
    transform-origin: left center;
    background-color: rgba(45, 127, 249, 0.15);
    border: 1px solid rgba(45, 127, 249, 0.2);
}

.floating-cube::after {
    right: 0;
    bottom: 100%;
    transform: rotateX(90deg);
    transform-origin: bottom center;
    background-color: rgba(45, 127, 249, 0.05);
    border: 1px solid rgba(45, 127, 249, 0.2);
}

.floating-sphere {
    position: absolute;
    width: 16px;
    height: 16px;
    background: radial-gradient(
        circle at 30% 30%,
        rgba(0, 194, 255, 0.4),
        rgba(45, 127, 249, 0.1)
    );
    border-radius: 50%;
    top: 20%;
    right: 15%;
    animation: floatObject 6s ease-in-out infinite alternate-reverse;
    box-shadow: 0 0 10px rgba(0, 194, 255, 0.2);
}

.floating-pyramid {
    position: absolute;
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-bottom: 25px solid rgba(45, 127, 249, 0.08);
    top: 70%;
    right: 20%;
    animation: floatObject 7s ease-in-out infinite alternate;
    transform-style: preserve-3d;
    transform: rotateY(45deg);
}

.floating-data-points {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.data-point {
    position: absolute;
    width: 4px;
    height: 4px;
    background-color: rgba(45, 127, 249, 0.5);
    border-radius: 50%;
    animation: dataPointPulse 3s infinite alternate;
}

.data-point:nth-child(1) {
    top: 20%;
    left: 30%;
    animation-delay: 0.2s;
}

.data-point:nth-child(2) {
    top: 60%;
    left: 20%;
    animation-delay: 0.4s;
}

.data-point:nth-child(3) {
    top: 40%;
    right: 25%;
    animation-delay: 0.6s;
}

.data-point:nth-child(4) {
    top: 70%;
    right: 40%;
    animation-delay: 0.8s;
}

.data-point:nth-child(5) {
    top: 30%;
    right: 10%;
    animation-delay: 1s;
}

.particle-system {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: radial-gradient(circle at center, rgba(45, 127, 249, 0.02) 0%, transparent 70%);
    animation: particleSystemPulse 5s infinite alternate;
}

/* Conectores */
.model-connectors {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 1;
    pointer-events: none;
}

.connector-line {
    position: absolute;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(45, 127, 249, 0.3), transparent);
    opacity: 0.6;
    animation: lineGlow 3s infinite alternate;
}

.connector-line:nth-child(1) {
    width: 80px;
    top: 40%;
    left: 30%;
    transform: rotate(30deg);
    animation-delay: 0.2s;
}

.connector-line:nth-child(2) {
    width: 120px;
    top: 60%;
    right: 20%;
    transform: rotate(-20deg);
    animation-delay: 0.4s;
}

.connector-line:nth-child(3) {
    width: 100px;
    bottom: 30%;
    left: 20%;
    transform: rotate(-45deg);
    animation-delay: 0.6s;
}

.connector-dot {
    position: absolute;
    width: 6px;
    height: 6px;
    background-color: rgba(0, 194, 255, 0.5);
    border-radius: 50%;
    animation: dotPulse 2s infinite alternate;
}

.connector-dot:nth-child(4) {
    top: 40%;
    left: 25%;
    animation-delay: 0.2s;
}

.connector-dot:nth-child(5) {
    top: 60%;
    right: 15%;
    animation-delay: 0.4s;
}

.connector-dot:nth-child(6) {
    bottom: 30%;
    left: 15%;
    animation-delay: 0.6s;
}

/* Novas animações para elementos 3D */
@keyframes basePulse {
    0% {
        opacity: 0.5;
        transform: translateY(60px) rotateX(60deg) scale(1);
    }
    100% {
        opacity: 0.7;
        transform: translateY(60px) rotateX(60deg) scale(1.1);
    }
}

@keyframes holoFloat {
    0% {
        transform: rotateX(10deg) rotateY(20deg) rotateZ(5deg) translateZ(0);
    }
    100% {
        transform: rotateX(15deg) rotateY(25deg) rotateZ(8deg) translateZ(10px);
    }
}

@keyframes holoPulse {
    0% {
        opacity: 0.7;
        transform: scale(1);
    }
    100% {
        opacity: 0.9;
        transform: scale(1.05);
    }
}

@keyframes scanLine {
    0% {
        transform: translateY(-20px);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateY(20px);
        opacity: 0;
    }
}

@keyframes labelFloat {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(-5px);
    }
}

@keyframes metricFloat {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(-8px);
    }
}

@keyframes floatObject {
    0% {
        transform: translateY(0) rotate(0);
    }
    50% {
        transform: translateY(-10px) rotate(5deg);
    }
    100% {
        transform: translateY(5px) rotate(-5deg);
    }
}

@keyframes dataPointPulse {
    0% {
        transform: scale(1);
        opacity: 0.5;
        box-shadow: 0 0 2px rgba(45, 127, 249, 0.3);
    }
    100% {
        transform: scale(1.5);
        opacity: 0.8;
        box-shadow: 0 0 8px rgba(45, 127, 249, 0.5);
    }
}

@keyframes particleSystemPulse {
    0% {
        opacity: 0.5;
        transform: scale(1);
    }
    100% {
        opacity: 0.8;
        transform: scale(1.1);
    }
}

@keyframes lineGlow {
    0% {
        opacity: 0.3;
        background-position: left center;
    }
    100% {
        opacity: 0.6;
        background-position: right center;
    }
}

@keyframes dotPulse {
    0% {
        transform: scale(1);
        opacity: 0.5;
        box-shadow: 0 0 2px rgba(0, 194, 255, 0.3);
    }
    100% {
        transform: scale(1.5);
        opacity: 0.8;
        box-shadow: 0 0 8px rgba(0, 194, 255, 0.5);
    }
}

/* Media queries */
@media (max-width: 1200px) {
    .hero {
        padding: 190px 0 80px;
    }

    .hero-content {
        max-width: 500px;
    }

    .hero-title {
        font-size: 48px;
    }

    .hero-visual {
        width: 40%;
    }
    
    .card-1 {
        right: -30px;
    }
    
    .card-2 {
        left: -30px;
    }
    
    .model-showcase {
        transform: scale(0.9);
    }
    
    .model-metrics {
        bottom: -20px;
    }
    
    .model-card.card-3 {
        top: 10%;
        right: -30px;
    }
    
    .floating-cube,
    .floating-sphere,
    .floating-pyramid {
        transform: scale(0.9);
    }
    
    .connector-line:nth-child(1) {
        width: 60px;
    }
    
    .connector-line:nth-child(2) {
        width: 90px;
    }
    
    .connector-line:nth-child(3) {
        width: 70px;
    }
}

@media (max-width: 992px) {
    .hero {
        padding: 180px 0 60px;
    }

    .hero .container {
        flex-direction: column;
        align-items: center;
        text-align: center;
        height: auto;
    }

    .hero-content {
        max-width: 100%;
        align-items: center;
    }

    .hero-title {
        font-size: 44px;
    }

    .hero-description {
        max-width: 600px;
    }
    
    .hero-badge {
        margin-left: auto;
        margin-right: auto;
    }

    .tech-icons {
        justify-content: center;
    }

    .hero-cta {
        justify-content: center;
        margin-bottom: 40px;
    }

    .hero-visual {
        position: relative;
        width: 100%;
        right: auto;
        top: auto;
        transform: none;
        margin-top: 40px;
        height: 400px;
    }

    .hero-scroll {
        margin: 20px auto 30px;
    }
    
    .model-container {
        margin: 0 auto;
    }
    
    .card-1 {
        right: 50px;
        top: 50px;
    }
    
    .card-2 {
        left: 50px;
        bottom: 50px;
    }
    
    .model-showcase {
        transform: scale(0.8);
    }
    
    .model-metrics {
        bottom: -40px;
        transform: translateX(-50%) scale(0.9);
    }
    
    .model-card.card-3 {
        display: none;
    }
    
    .model-platform {
        width: 150px;
        height: 150px;
    }
    
    .platform-ring {
        width: 130px;
        height: 130px;
    }
    
    .hologram-effect {
        width: 60px;
        height: 60px;
    }
    
    .floating-data-points {
        opacity: 0.5;
    }
    
    .connector-line {
        opacity: 0.4;
    }
}

@media (max-width: 768px) {
    .hero {
        padding: 170px 0 40px;
    }

    .hero-title {
        font-size: 38px;
    }

    .hero-description {
        font-size: 18px;
    }

    .hero-badge {
        margin-bottom: 16px;
    }

    .tech-icons {
        gap: 12px;
        margin-bottom: 24px;
    }

    .tech-icon {
        width: 36px;
        height: 36px;
    }

    .tech-icon i {
        font-size: 16px;
    }

    .hero-visual {
        height: 300px;
    }

    .model-container {
        width: 250px;
        height: 250px;
    }

    .card-1, .card-2 {
        transform: none;
    }
    
    .model-showcase {
        transform: scale(0.7);
    }
    
    .model-metrics {
        flex-direction: column;
        gap: 10px;
        bottom: -90px;
    }
    
    .model-platform {
        width: 120px;
        height: 120px;
    }
    
    .platform-ring {
        width: 100px;
        height: 100px;
    }
    
    .hologram-effect {
        width: 50px;
        height: 50px;
    }
    
    .model-labels {
        top: -10px;
        right: -50px;
    }
    
    .label-text {
        font-size: 10px;
    }
    
    .model-floating-elements {
        opacity: 0.6;
        transform: scale(0.8);
    }
    
    .model-connectors {
        display: none;
    }
}

@media (max-width: 576px) {
    .hero {
        padding: 160px 0 40px;
    }

    .hero-title {
        font-size: 32px;
    }

    .hero-description {
        font-size: 16px;
    }

    .hero-cta {
        flex-direction: column;
        width: 100%;
    }
    
    .hero-cta .button {
        width: 100%;
        text-align: center;
    }

    .hero-visual {
        height: 250px;
    }
    
    .card-1 {
        right: 20px;
        top: 30px;
    }
    
    .card-2 {
        left: 20px;
        bottom: 30px;
    }
    
    .model-showcase {
        transform: scale(0.6);
    }
    
    .model-metrics {
        display: none;
    }
    
    .model-platform {
        width: 100px;
        height: 100px;
    }
    
    .platform-ring {
        width: 90px;
        height: 90px;
    }
    
    .hologram-effect {
        width: 40px;
        height: 40px;
    }
    
    .model-labels {
        display: none;
    }
    
    .floating-data-points,
    .particle-system {
        opacity: 0.3;
    }
    
    .floating-cube,
    .floating-sphere,
    .floating-pyramid {
        transform: scale(0.7);
    }
}

/* Correção específica para zoom baixo */
@media (min-resolution: 0.67dppx) {
    .hero {
        padding-top: 220px;
    }
    
    .hero-badge {
        margin-top: 40px;
    }
    
    .hero .container {
        height: calc(100vh - 320px);
    }
}