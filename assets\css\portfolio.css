.projects-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    padding: 2rem 0;
}

.project-card {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.project-card:hover {
    transform: translateY(-5px);
}

.project-image {
    position: relative;
    width: 100%;
    height: 400px;
    overflow: hidden;
    border-radius: 8px 8px 0 0;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
}

.project-swiper {
    width: 100%;
    height: 100%;
}

.swiper-slide {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    width: 100%;
    height: 100%;
    padding: 1rem;
}

.project-img {
    width: auto;
    height: auto;
    object-fit: contain;
    display: block;
    max-width: 100%;
    max-height: 100%;
    object-position: center;
}

.project-content {
    padding: 1.5rem;
}

.project-content h3 {
    font-size: 1.5rem;
    margin-bottom: 0.75rem;
    color: #333;
}

.project-content p {
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.project-tech span {
    background: #f0f0f0;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    color: #555;
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    padding: 1rem;
}

.project-tags span {
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
}

.swiper-button-next,
.swiper-button-prev {
    color: #fff;
    background: rgba(0, 0, 0, 0.5);
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.swiper-button-next:after,
.swiper-button-prev:after {
    font-size: 1.2rem;
}

.swiper-pagination-bullet {
    background: #fff;
    opacity: 0.5;
}

.swiper-pagination-bullet-active {
    opacity: 1;
    background: #fff;
}

.project-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    font-size: 0.875rem;
    color: #666;
}

.project-status i {
    font-size: 1rem;
}

.project-status i.fa-check-circle {
    color: #4CAF50;
}

.project-status i.fa-clock {
    color: #FFA726;
}

.project-link {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
    padding: 10px 20px;
    border-radius: 6px;
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.project-link i {
    margin-left: 8px;
}

.project-link:hover {
    background-color: var(--primary-color-dark);
    transform: translateY(-2px);
}

.project-link.disabled {
    background-color: #e0e0e0;
    color: #666;
    cursor: not-allowed;
}

.project-link.disabled:hover {
    background-color: #e0e0e0;
    transform: none;
}

@media (max-width: 1024px) {
    .projects-grid {
        grid-template-columns: 1fr;
    }
    
    .project-image {
        height: 350px;
        padding: 0.5rem;
    }
}

@media (max-width: 768px) {
    .project-image {
        height: 300px;
        padding: 0.5rem;
    }
    
    .project-content {
        padding: 1rem;
    }
    
    .project-content h3 {
        font-size: 1.25rem;
    }
    
    .project-tech span {
        font-size: 0.75rem;
        padding: 0.2rem 0.5rem;
    }
    
    .project-tags span {
        font-size: 0.75rem;
        padding: 0.2rem 0.5rem;
    }
    
    .project-link {
        font-size: 0.875rem;
    }
    
    .swiper-slide {
        padding: 0.5rem;
    }
} 