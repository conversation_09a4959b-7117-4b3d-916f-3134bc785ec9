/* ===== ASIMOV COMMERCE STYLES - RED/ORANGE/PINK THEME ===== */

/* Override ASIMOV logo colors to use blue theme */
.navbar .logo-primary,
.footer .logo-primary {
    color: var(--asimov-blue) !important;
}

/* Force center alignment for section titles - Override style.css conflicts */
#teemoflow .section-title,
.commerce-products .section-title,
.product-hero-header .section-title,
.pricing-section .section-title,
.products-grid-section .section-title,
.section-title {
    text-align: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
    display: block !important;
    width: 100% !important;
    left: auto !important;
    transform: none !important;
    position: relative !important;
}

/* Ensure containers are also centered */
.product-hero-header,
.commerce-products .section-header,
.pricing-section .section-header,
.products-grid-section .section-header,
.section-header {
    text-align: center !important;
    width: 100% !important;
    display: block !important;
}

:root {
    /* Commerce Color Palette - Red/Orange/Pink Theme */
    --commerce-primary: #ff6b6b;            /* Primary Red-Pink */
    --commerce-secondary: #ee5a24;          /* Secondary Orange */
    --commerce-tertiary: #ff9ff3;           /* Pink Accent */
    --commerce-accent: #feca57;             /* Yellow Accent */
    --commerce-accent-orange: #ff7675;      /* Light Red */
    --commerce-light: #f8fafc;              /* Light Background */
    --commerce-dark: #1a202c;               /* Dark Text */
    --commerce-text-light: #F5F5F5;         /* Light Text */
    --commerce-text-muted: #CCCCCC;         /* Muted Text */

    /* ASIMOV Brand Colors - Blue Theme */
    --asimov-blue: #2D7FF9;                 /* Primary Blue for ASIMOV logo */
    --asimov-blue-dark: #1C57B5;            /* Darker Blue */

    /* Gradients */
    --commerce-gradient-primary: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 50%, #ff9ff3 100%);
    --commerce-gradient-light: linear-gradient(135deg, #f8fafc 0%, #e6f0ff 100%);
    --commerce-gradient-accent: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
    --commerce-gradient-badge: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

/* Mobile menu styles for commerce page */
.mobile-menu {
    position: fixed;
    top: 80px;
    left: 0;
    right: 0;
    background: white;
    padding: 1rem;
    border-bottom: 1px solid var(--neutral-200);
    transform: translateY(-100%);
    transition: transform 0.3s ease;
    z-index: 999;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.mobile-menu.active {
    transform: translateY(0);
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    color: var(--neutral-700);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.mobile-nav-link:hover {
    background: var(--primary-light);
    color: var(--primary-color);
}

.mobile-nav-link i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.mobile-menu-button {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
}

.mobile-menu-button span {
    width: 25px;
    height: 3px;
    background: var(--neutral-700);
    border-radius: 2px;
    transition: all 0.3s ease;
}

.mobile-menu-button.active span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-button.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-button.active span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Commerce Hero Section Enhancements */
.commerce-hero {
    background: var(--commerce-gradient-primary);
    position: relative;
    overflow: hidden;
}

.commerce-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="commerce-grid" width="25" height="25" patternUnits="userSpaceOnUse"><circle cx="12.5" cy="12.5" r="2" fill="rgba(255,255,255,0.2)"/><path d="M 8 12.5 L 17 12.5 M 12.5 8 L 12.5 17" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23commerce-grid)"/></svg>');
    opacity: 0.3;
}

.commerce-hero .hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: 3rem;
    max-width: 600px;
    opacity: 1;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    color: white;
    font-weight: 400;
}

/* Product Hero Section */
.product-hero {
    background: var(--commerce-light);
    padding: 6rem 0;
}

.product-hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

/* TeemoFlow Dashboard Enhancements */
.product-preview {
    padding: 2rem;
    background: white;
    border-radius: 16px;
    border: 1px solid var(--commerce-secondary);
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.1);
}

.preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: var(--commerce-light);
    border-radius: 12px 12px 0 0;
    border-bottom: 1px solid var(--neutral-200);
}

.preview-controls {
    display: flex;
    gap: 0.5rem;
}

.control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--neutral-300);
}

.control:nth-child(1) {
    background: #ff5f56;
}

.control:nth-child(2) {
    background: #ffbd2e;
}

.control:nth-child(3) {
    background: #27ca3f;
}

.preview-title {
    font-weight: 600;
    color: var(--commerce-secondary);
}

.commerce-dashboard {
    padding: 1.5rem;
    background: var(--commerce-light);
    border-radius: 12px;
}

.dashboard-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.metric-card {
    background: white;
    padding: 1rem;
    border-radius: 12px;
    border: 1px solid var(--neutral-200);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    position: relative;
}

.metric-icon {
    width: 40px;
    height: 40px;
    background: var(--commerce-gradient-primary);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.metric-data {
    flex: 1;
}

.metric-value {
    display: block;
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--commerce-primary);
}

.metric-label {
    display: block;
    font-size: 0.8rem;
    color: var(--neutral-600);
}

.metric-trend {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.8rem;
    font-weight: 600;
}

.metric-trend.up {
    color: #10b981;
}

.metric-trend.down {
    color: #ef4444;
}

.chat-preview {
    background: white;
    border-radius: 12px;
    border: 1px solid var(--neutral-200);
    overflow: hidden;
}

.chat-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background: var(--commerce-light);
    border-bottom: 1px solid var(--neutral-200);
    font-weight: 600;
    color: var(--commerce-secondary);
}

.chat-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-left: auto;
}

.chat-status.online {
    background: #10b981;
}

.chat-messages {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.message {
    max-width: 80%;
    padding: 0.5rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
}

.message.bot {
    background: var(--commerce-light);
    color: var(--commerce-dark);
    align-self: flex-start;
}

.message.user {
    background: var(--commerce-gradient-primary);
    color: white;
    align-self: flex-end;
}

/* Pricing Section Enhancements */
.pricing-section {
    background: var(--commerce-gradient-light);
    padding: 6rem 0;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 3rem;
}

.pricing-card {
    background: white;
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(254, 202, 87, 0.2);
}

.pricing-card.featured {
    border: 2px solid var(--commerce-accent);
    transform: scale(1.05);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.pricing-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--commerce-gradient-accent);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Products Grid Section */
.products-grid-section {
    background: white;
    padding: 6rem 0;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.product-card {
    background: white;
    border: 1px solid var(--neutral-200);
    border-radius: 16px;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(255, 159, 243, 0.15);
    border-color: var(--commerce-tertiary);
}

.product-icon {
    width: 60px;
    height: 60px;
    background: var(--commerce-gradient-primary);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.product-icon i {
    font-size: 1.5rem;
    color: white;
}

/* Status badges */
.status-badge.active {
    background: #dcfce7;
    color: #166534;
}

.status-badge.beta {
    background: #dbeafe;
    color: #1e40af;
}

.status-badge.planning {
    background: #fef3c7;
    color: #92400e;
}

/* CTA Section */
.cta-section {
    background: var(--commerce-gradient-primary);
    color: white;
    padding: 6rem 0;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Button Styles */
.button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    text-align: center;
    justify-content: center;
}

.button-primary {
    background: var(--commerce-accent);
    color: var(--commerce-primary);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.button-primary:hover {
    background: var(--commerce-accent-blue);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
}

.button-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.button-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.button-outline {
    background: transparent;
    color: var(--commerce-accent);
    border: 2px solid var(--commerce-accent);
}

.button-outline:hover {
    background: var(--commerce-accent);
    color: white;
    border-color: var(--commerce-accent);
}

.button i {
    font-size: 1.1rem;
}

/* Pricing Section Button Overrides */
.pricing-card .button-outline {
    background: transparent;
    color: var(--commerce-accent);
    border: 2px solid var(--commerce-accent);
}

.pricing-card .button-outline:hover {
    background: var(--commerce-accent);
    color: white;
    border-color: var(--commerce-accent);
}

.pricing-card .button-primary {
    background: var(--commerce-accent);
    color: white;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.pricing-card .button-primary:hover {
    background: var(--commerce-accent-blue);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
}

/* Responsividade */
@media (max-width: 1200px) {
    .pricing-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }

    .product-hero-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
}

@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .mobile-menu-button {
        display: flex;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .commerce-hero {
        padding: 6rem 0 4rem;
    }

    .commerce-hero .hero-title {
        font-size: 2.5rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .dashboard-metrics {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .metric-card {
        padding: 0.75rem;
    }

    .commerce-dashboard {
        padding: 1rem;
    }

    .chat-messages {
        padding: 0.75rem;
    }

    .message {
        font-size: 0.75rem;
        max-width: 90%;
    }

    .product-preview {
        padding: 1rem;
    }
}
