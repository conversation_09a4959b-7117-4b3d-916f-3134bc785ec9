<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8" />
    <title>Tecnologias - ASIMOV TECH SOLUTIONS</title>
    <link rel="icon" href="assets/img/favicon/favicon.png" type="image/png">
    <meta name="description" content="Conheça as tecnologias utilizadas pela ASIMOV TECH SOLUTIONS" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta property="og:image" content="img/banner.jpg">
    <meta property="og:image:type" content="image/jpeg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/pages.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar">
        <div class="container navbar-container">
            <div class="nav-logo"><a href="index.html" class="logo-link"><img src="assets/img/logo.png" alt="ASIMOV TECH SOLUTIONS Logo"><div class="logo-text"><span class="logo-primary">ASIMOV</span><span class="logo-secondary">TECH SOLUTIONS</span></div></a></div>

            <div class="nav-links">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>Início</span>
                </a>
                <a href="produtos.html" class="nav-link">
                    <i class="fas fa-box"></i>
                    <span>Produtos</span>
                </a>
                <a href="portfolio.html" class="nav-link">
                    <i class="fas fa-project-diagram"></i>
                    <span>Portfólio</span>
                </a>
                <a href="blog.html" class="nav-link">
                    <i class="fas fa-blog"></i>
                    <span>Blog</span>
                </a>
                <a href="carreiras.html" class="nav-link">
                    <i class="fas fa-briefcase"></i>
                    <span>Carreiras</span>
                </a>
                <a href="tecnologias.html" class="nav-link active">
                    <i class="fas fa-microchip"></i>
                    <span>Tecnologias</span>
                </a>
                <a href="https://api.whatsapp.com/send/?phone=5521982301476&text&type=phone_number&app_absent=0" class="nav-button">
                    <i class="fas fa-paper-plane"></i>
                    <span>Iniciar Projeto</span>
                </a>
            </div>

            <button class="mobile-menu-button">
                <span></span>
                <span></span>
                <span></span>
            </button>
        </div>
    </nav>

    <!-- Menu Mobile -->
    <div class="mobile-menu">
        <a href="index.html" class="mobile-nav-link">
            <i class="fas fa-home"></i>
            <span>Início</span>
        </a>
        <a href="produtos.html" class="mobile-nav-link">
            <i class="fas fa-box"></i>
            <span>Produtos</span>
        </a>
        <a href="portfolio.html" class="mobile-nav-link">
            <i class="fas fa-project-diagram"></i>
            <span>Portfólio</span>
        </a>
        <a href="blog.html" class="mobile-nav-link">
            <i class="fas fa-blog"></i>
            <span>Blog</span>
        </a>
        <a href="carreiras.html" class="mobile-nav-link">
            <i class="fas fa-briefcase"></i>
            <span>Carreiras</span>
        </a>
        <a href="tecnologias.html" class="mobile-nav-link active">
            <i class="fas fa-microchip"></i>
            <span>Tecnologias</span>
        </a>
        <a href="https://api.whatsapp.com/send/?phone=5521982301476&text&type=phone_number&app_absent=0" class="mobile-nav-link">
            <i class="fas fa-paper-plane"></i>
            <span>Iniciar Projeto</span>
        </a>
    </div>

    <section class="technologies-header">
        <div class="container">
            <div class="header-content">
                <h1 class="main-title">Nossas <span class="highlight">Tecnologias</span></h1>
                <p class="subtitle">Conheça as ferramentas e plataformas que utilizamos para criar soluções inovadoras</p>
            </div>
        </div>
    </section>

    <section class="tech-categories">
        <div class="container">
            <div class="category-filters">
                <button class="category-filter active" data-filter="all">Todas</button>
                <button class="category-filter" data-filter="frontend">Frontend</button>
                <button class="category-filter" data-filter="backend">Backend</button>
                <button class="category-filter" data-filter="mobile">Mobile</button>
                <button class="category-filter" data-filter="data">Dados e IA</button>
                <button class="category-filter" data-filter="cloud">Cloud</button>
                <button class="category-filter" data-filter="devops">DevOps</button>
            </div>

            <div class="tech-grid">
                <!-- Frontend -->
                <div class="tech-card" data-category="frontend">
                    <div class="tech-icon">
                        <i class="fab fa-react"></i>
                    </div>
                    <h3>React</h3>
                    <p>Biblioteca JavaScript para construção de interfaces de usuário interativas e responsivas.</p>
                    <div class="tech-expertise">
                        <div class="expertise-label">Nível de Expertise</div>
                        <div class="expertise-bar">
                            <div class="expertise-fill" style="width: 95%"></div>
                        </div>
                    </div>
                </div>

                <div class="tech-card" data-category="frontend">
                    <div class="tech-icon">
                        <i class="fab fa-vuejs"></i>
                    </div>
                    <h3>Vue.js</h3>
                    <p>Framework progressivo para a construção de interfaces de usuário.</p>
                    <div class="tech-expertise">
                        <div class="expertise-label">Nível de Expertise</div>
                        <div class="expertise-bar">
                            <div class="expertise-fill" style="width: 90%"></div>
                        </div>
                    </div>
                </div>

                <div class="tech-card" data-category="frontend">
                    <div class="tech-icon">
                        <i class="fab fa-angular"></i>
                    </div>
                    <h3>Angular</h3>
                    <p>Plataforma para construção de aplicações web e mobile.</p>
                    <div class="tech-expertise">
                        <div class="expertise-label">Nível de Expertise</div>
                        <div class="expertise-bar">
                            <div class="expertise-fill" style="width: 85%"></div>
                        </div>
                    </div>
                </div>

                <div class="tech-card" data-category="frontend">
                    <div class="tech-icon">
                        <i class="fab fa-js-square"></i>
                    </div>
                    <h3>TypeScript</h3>
                    <p>Superset de JavaScript que adiciona tipagem estática.</p>
                    <div class="tech-expertise">
                        <div class="expertise-label">Nível de Expertise</div>
                        <div class="expertise-bar">
                            <div class="expertise-fill" style="width: 90%"></div>
                        </div>
                    </div>
                </div>

                <!-- Backend -->
                <div class="tech-card" data-category="backend">
                    <div class="tech-icon">
                        <i class="fab fa-node-js"></i>
                    </div>
                    <h3>Node.js</h3>
                    <p>Ambiente de execução JavaScript server-side para desenvolvimento de aplicações escaláveis.</p>
                    <div class="tech-expertise">
                        <div class="expertise-label">Nível de Expertise</div>
                        <div class="expertise-bar">
                            <div class="expertise-fill" style="width: 95%"></div>
                        </div>
                    </div>
                </div>

                <div class="tech-card" data-category="backend">
                    <div class="tech-icon">
                        <i class="fab fa-python"></i>
                    </div>
                    <h3>Python</h3>
                    <p>Linguagem versátil para desenvolvimento web, análise de dados e inteligência artificial.</p>
                    <div class="tech-expertise">
                        <div class="expertise-label">Nível de Expertise</div>
                        <div class="expertise-bar">
                            <div class="expertise-fill" style="width: 90%"></div>
                        </div>
                    </div>
                </div>

                <div class="tech-card" data-category="backend">
                    <div class="tech-icon">
                        <i class="fas fa-gem"></i>
                    </div>
                    <h3>Ruby on Rails</h3>
                    <p>Framework web que segue o padrão MVC e prioriza convenção sobre configuração.</p>
                    <div class="tech-expertise">
                        <div class="expertise-label">Nível de Expertise</div>
                        <div class="expertise-bar">
                            <div class="expertise-fill" style="width: 80%"></div>
                        </div>
                    </div>
                </div>

                <div class="tech-card" data-category="backend">
                    <div class="tech-icon">
                        <i class="fab fa-java"></i>
                    </div>
                    <h3>Java & Spring</h3>
                    <p>Ecossistema robusto para desenvolvimento de aplicações empresariais.</p>
                    <div class="tech-expertise">
                        <div class="expertise-label">Nível de Expertise</div>
                        <div class="expertise-bar">
                            <div class="expertise-fill" style="width: 85%"></div>
                        </div>
                    </div>
                </div>

                <!-- Mobile -->
                <div class="tech-card" data-category="mobile">
                    <div class="tech-icon">
                        <i class="fab fa-react"></i>
                    </div>
                    <h3>React Native</h3>
                    <p>Framework para desenvolvimento de aplicativos móveis multiplataforma.</p>
                    <div class="tech-expertise">
                        <div class="expertise-label">Nível de Expertise</div>
                        <div class="expertise-bar">
                            <div class="expertise-fill" style="width: 90%"></div>
                        </div>
                    </div>
                </div>

                <div class="tech-card" data-category="mobile">
                    <div class="tech-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>Flutter</h3>
                    <p>SDK da Google para criar aplicativos nativos de alta qualidade para iOS e Android.</p>
                    <div class="tech-expertise">
                        <div class="expertise-label">Nível de Expertise</div>
                        <div class="expertise-bar">
                            <div class="expertise-fill" style="width: 85%"></div>
                        </div>
                    </div>
                </div>

                <div class="tech-card" data-category="mobile">
                    <div class="tech-icon">
                        <i class="fab fa-apple"></i>
                    </div>
                    <h3>iOS (Swift)</h3>
                    <p>Desenvolvimento nativo de aplicativos para o ecossistema Apple.</p>
                    <div class="tech-expertise">
                        <div class="expertise-label">Nível de Expertise</div>
                        <div class="expertise-bar">
                            <div class="expertise-fill" style="width: 80%"></div>
                        </div>
                    </div>
                </div>

                <div class="tech-card" data-category="mobile">
                    <div class="tech-icon">
                        <i class="fab fa-android"></i>
                    </div>
                    <h3>Android (Kotlin)</h3>
                    <p>Desenvolvimento nativo de aplicativos para o ecossistema Android.</p>
                    <div class="tech-expertise">
                        <div class="expertise-label">Nível de Expertise</div>
                        <div class="expertise-bar">
                            <div class="expertise-fill" style="width: 80%"></div>
                        </div>
                    </div>
                </div>

                <!-- Dados e IA -->
                <div class="tech-card" data-category="data">
                    <div class="tech-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3>TensorFlow</h3>
                    <p>Biblioteca de código aberto para aprendizado de máquina e inteligência artificial.</p>
                    <div class="tech-expertise">
                        <div class="expertise-label">Nível de Expertise</div>
                        <div class="expertise-bar">
                            <div class="expertise-fill" style="width: 85%"></div>
                        </div>
                    </div>
                </div>

                <div class="tech-card" data-category="data">
                    <div class="tech-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h3>PyTorch</h3>
                    <p>Framework de aprendizado profundo para pesquisa e produção.</p>
                    <div class="tech-expertise">
                        <div class="expertise-label">Nível de Expertise</div>
                        <div class="expertise-bar">
                            <div class="expertise-fill" style="width: 80%"></div>
                        </div>
                    </div>
                </div>

                <div class="tech-card" data-category="data">
                    <div class="tech-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h3>SQL & NoSQL</h3>
                    <p>Implementação de bancos de dados relacionais e não-relacionais.</p>
                    <div class="tech-expertise">
                        <div class="expertise-label">Nível de Expertise</div>
                        <div class="expertise-bar">
                            <div class="expertise-fill" style="width: 90%"></div>
                        </div>
                    </div>
                </div>

                <div class="tech-card" data-category="data">
                    <div class="tech-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3>Computer Vision</h3>
                    <p>Desenvolvimento de sistemas de visão computacional para reconhecimento e análise de imagens.</p>
                    <div class="tech-expertise">
                        <div class="expertise-label">Nível de Expertise</div>
                        <div class="expertise-bar">
                            <div class="expertise-fill" style="width: 85%"></div>
                        </div>
                    </div>
                </div>

                <!-- Cloud -->
                <div class="tech-card" data-category="cloud">
                    <div class="tech-icon">
                        <i class="fab fa-aws"></i>
                    </div>
                    <h3>AWS</h3>
                    <p>Plataforma de computação em nuvem com serviços abrangentes.</p>
                    <div class="tech-expertise">
                        <div class="expertise-label">Nível de Expertise</div>
                        <div class="expertise-bar">
                            <div class="expertise-fill" style="width: 90%"></div>
                        </div>
                    </div>
                </div>

                <div class="tech-card" data-category="cloud">
                    <div class="tech-icon">
                        <i class="fab fa-google"></i>
                    </div>
                    <h3>Google Cloud</h3>
                    <p>Soluções de infraestrutura e serviços gerenciados da Google.</p>
                    <div class="tech-expertise">
                        <div class="expertise-label">Nível de Expertise</div>
                        <div class="expertise-bar">
                            <div class="expertise-fill" style="width: 85%"></div>
                        </div>
                    </div>
                </div>

                <div class="tech-card" data-category="cloud">
                    <div class="tech-icon">
                        <i class="fab fa-microsoft"></i>
                    </div>
                    <h3>Azure</h3>
                    <p>Plataforma de nuvem da Microsoft para construção e gerenciamento de aplicações.</p>
                    <div class="tech-expertise">
                        <div class="expertise-label">Nível de Expertise</div>
                        <div class="expertise-bar">
                            <div class="expertise-fill" style="width: 85%"></div>
                        </div>
                    </div>
                </div>

                <!-- DevOps -->
                <div class="tech-card" data-category="devops">
                    <div class="tech-icon">
                        <i class="fab fa-docker"></i>
                    </div>
                    <h3>Docker</h3>
                    <p>Plataforma de containerização para desenvolvimento, envio e execução de aplicações.</p>
                    <div class="tech-expertise">
                        <div class="expertise-label">Nível de Expertise</div>
                        <div class="expertise-bar">
                            <div class="expertise-fill" style="width: 95%"></div>
                        </div>
                    </div>
                </div>

                <div class="tech-card" data-category="devops">
                    <div class="tech-icon">
                        <i class="fas fa-dharmachakra"></i>
                    </div>
                    <h3>Kubernetes</h3>
                    <p>Sistema de orquestração de containers para automação de implantação e gerenciamento.</p>
                    <div class="tech-expertise">
                        <div class="expertise-label">Nível de Expertise</div>
                        <div class="expertise-bar">
                            <div class="expertise-fill" style="width: 90%"></div>
                        </div>
                    </div>
                </div>

                <div class="tech-card" data-category="devops">
                    <div class="tech-icon">
                        <i class="fab fa-github"></i>
                    </div>
                    <h3>CI/CD</h3>
                    <p>Implementação de pipelines de integração e entrega contínua.</p>
                    <div class="tech-expertise">
                        <div class="expertise-label">Nível de Expertise</div>
                        <div class="expertise-bar">
                            <div class="expertise-fill" style="width: 90%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="tech-methodology">
        <div class="container">
            <div class="methodology-content">
                <div class="methodology-text">
                    <h2>Nossa Metodologia de Desenvolvimento</h2>
                    <p>Na ASIMOV TECH SOLUTIONS, adotamos uma abordagem moderna e eficiente para o desenvolvimento de software, combinando as melhores práticas ágeis com processos rigorosos de qualidade.</p>

                    <div class="methodology-steps">
                        <div class="methodology-step">
                            <div class="step-number">01</div>
                            <div class="step-content">
                                <h3>Discovery</h3>
                                <p>Entendemos profundamente os requisitos do projeto e as necessidades do cliente para definir a melhor abordagem técnica.</p>
                            </div>
                        </div>

                        <div class="methodology-step">
                            <div class="step-number">02</div>
                            <div class="step-content">
                                <h3>Planejamento</h3>
                                <p>Definimos a arquitetura, escolhemos as tecnologias apropriadas e estabelecemos um cronograma detalhado.</p>
                            </div>
                        </div>

                        <div class="methodology-step">
                            <div class="step-number">03</div>
                            <div class="step-content">
                                <h3>Desenvolvimento</h3>
                                <p>Utilizamos metodologias ágeis com sprints semanais ou quinzenais, entregando incrementos funcionais do produto.</p>
                            </div>
                        </div>

                        <div class="methodology-step">
                            <div class="step-number">04</div>
                            <div class="step-content">
                                <h3>Testes & QA</h3>
                                <p>Implementamos testes automatizados e realizamos revisões rigorosas de qualidade em cada etapa.</p>
                            </div>
                        </div>

                        <div class="methodology-step">
                            <div class="step-number">05</div>
                            <div class="step-content">
                                <h3>Implantação</h3>
                                <p>Utilizamos pipelines de CI/CD para garantir implantações seguras e eficientes em ambientes de produção.</p>
                            </div>
                        </div>

                        <div class="methodology-step">
                            <div class="step-number">06</div>
                            <div class="step-content">
                                <h3>Suporte & Evolução</h3>
                                <p>Oferecemos suporte contínuo e implementamos melhorias baseadas em feedback e análise de desempenho.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="methodology-image">
                    <img src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80" alt="Metodologia de Desenvolvimento">
                </div>
            </div>
        </div>
    </section>

    <section class="tech-partners">
        <div class="container">
            <h2 class="section-title">Parceiros Tecnológicos</h2>
            <p class="section-subtitle">Trabalhamos com os principais líderes da indústria para entregar as melhores soluções</p>

            <div class="partners-grid">
                <div class="partner-item">
                    <i class="fab fa-aws fa-4x"></i>
                    <span>Amazon Web Services</span>
                </div>
                <div class="partner-item">
                    <i class="fab fa-google fa-4x"></i>
                    <span>Google Cloud</span>
                </div>
                <div class="partner-item">
                    <i class="fab fa-microsoft fa-4x"></i>
                    <span>Microsoft Azure</span>
                </div>
                <div class="partner-item">
                    <i class="fas fa-cloud fa-4x"></i>
                    <span>IBM Cloud</span>
                </div>
                <div class="partner-item">
                    <i class="fas fa-database fa-4x"></i>
                    <span>Oracle Cloud</span>
                </div>
                <div class="partner-item">
                    <i class="fab fa-salesforce fa-4x"></i>
                    <span>Salesforce</span>
                </div>
            </div>
        </div>
    </section>

    <section class="tech-cta">
        <div class="container">
            <div class="cta-content">
                <h2>Pronto para implementar as melhores tecnologias no seu negócio?</h2>
                <p>Entre em contato conosco para discutir como podemos ajudar sua empresa a se transformar digitalmente.</p>
                <div class="cta-buttons">
                    <a href="https://api.whatsapp.com/send/?phone=5521982301476&text&type=phone_number&app_absent=0" class="button button-primary">
                        <i class="fas fa-paper-plane"></i> Iniciar Projeto
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-grid">
                <!-- Coluna 1: Sobre -->
                <div class="footer-column">
                    <div class="footer-brand">
                        <img src="assets/img/logo.png" alt="ASIMOV TECH SOLUTIONS Logo" class="footer-logo">
                        <h3>ASIMOV TECH</h3>
                    </div>
                    <p class="footer-description">Transformando o futuro através da inovação tecnológica. Soluções personalizadas para impulsionar seu negócio.</p>
                    <div class="footer-social">
                        <a href="https://www.linkedin.com/company/asimovink" class="social-link" aria-label="LinkedIn">
                            <i class="fab fa-linkedin"></i>
                        </a>
                        <a href="https://github.com/asimovtechsolutions" class="social-link" aria-label="GitHub">
                            <i class="fab fa-github"></i>
                        </a>
                        <a href="https://api.whatsapp.com/send/?phone=5521982301476" class="social-link" aria-label="WhatsApp">
                            <i class="fab fa-whatsapp"></i>
                        </a>
                        <a href="https://instagram.com/asimovtech.systems" class="social-link" aria-label="Instagram">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>

                <!-- Coluna 2: Links Rápidos -->
                <div class="footer-column">
                    <h4 class="footer-title">Links Rápidos</h4>
                    <ul class="footer-links">
                        <li><a href="index.html#sobre">Sobre Nós</a></li>
                        <li><a href="index.html#servicos">Nossos Serviços</a></li>
                        <li><a href="portfolio.html">Portfólio</a></li>
                        <li><a href="blog.html">Blog</a></li>
                        <li><a href="carreiras.html">Carreiras</a></li>
                        <li><a href="tecnologias.html">Tecnologias</a></li>
                        <li><a href="index.html#cases">Cases de Sucesso</a></li>
                        <li><a href="index.html#contato">Fale Conosco</a></li>
                    </ul>
                </div>

                <!-- Coluna 3: Serviços -->
                <div class="footer-column">
                    <h4 class="footer-title">Nossos Serviços</h4>
                    <ul class="footer-links">
                        <li><a href="index.html#servicos">Desenvolvimento de Software</a></li>
                        <li><a href="index.html#servicos">Inteligência Artificial</a></li>
                        <li><a href="index.html#servicos">Automação de Processos</a></li>
                        <li><a href="index.html#servicos">Consultoria em TI</a></li>
                        <li><a href="index.html#servicos">Cloud Computing</a></li>
                    </ul>
                </div>

                <!-- Coluna 4: Contato -->
                <div class="footer-column">
                    <h4 class="footer-title">Contato</h4>
                    <ul class="footer-contact">
                        <li>
                            <i class="fas fa-phone"></i>
                            <span>+55 21 98230-1476</span>
                        </li>
                        <li>
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </li>
                        <li>
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Rio de Janeiro, RJ</span>
                        </li>
                        <li>
                            <i class="fas fa-clock"></i>
                            <span>Seg-Sex: 9h às 18h</span>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="footer-copyright">
                    <p>&copy; 2024 ASIMOV TECH SOLUTIONS. Todos os direitos reservados.</p>
                </div>
                <div class="footer-legal">
                    <a href="privacidade.html">Política de Privacidade</a>
                    <a href="termos.html">Termos de Uso</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="assets/js/script.js"></script>
    <script src="assets/js/structured-data.js"></script>
    <script>
        // Filtro de tecnologias
        document.addEventListener('DOMContentLoaded', function() {
            const filterButtons = document.querySelectorAll('.category-filter');
            const techCards = document.querySelectorAll('.tech-card');

            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remover classe ativa de todos os botões
                    filterButtons.forEach(btn => btn.classList.remove('active'));

                    // Adicionar classe ativa ao botão clicado
                    this.classList.add('active');

                    // Filtrar os cards
                    const filter = this.getAttribute('data-filter');

                    techCards.forEach(card => {
                        if (filter === 'all' || card.getAttribute('data-category') === filter) {
                            card.style.display = 'block';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            });
        });
    </script>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXXXX"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-XXXXXXXXXX');
    </script>
</body>
</html>

