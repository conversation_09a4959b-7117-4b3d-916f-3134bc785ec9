/* ===== ASIMOV RESEARCH STYLES - DARK BLUE/CYAN THEME ===== */

/* Override ASIMOV logo colors to use blue theme */
.navbar .logo-primary,
.footer .logo-primary {
    color: var(--asimov-blue) !important;
}

/* Force center alignment for section titles - Override style.css conflicts */
#quantum-protocol .section-title,
.research-products .section-title,
.product-hero-header .section-title,
.pricing-section .section-title,
.products-grid-section .section-title,
.section-title {
    text-align: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
    display: block !important;
    width: 100% !important;
    left: auto !important;
    transform: none !important;
    position: relative !important;
}

/* Ensure containers are also centered */
.product-hero-header,
.research-products .section-header,
.pricing-section .section-header,
.products-grid-section .section-header,
.section-header {
    text-align: center !important;
    width: 100% !important;
    display: block !important;
}

:root {
    /* Research Color Palette - Dark Blue & Cyan Theme */
    --research-primary: #1a1a2e;            /* Primary Dark Blue */
    --research-secondary: #16213e;          /* Secondary Navy */
    --research-tertiary: #0f3460;           /* Tertiary Deep Blue */
    --research-accent: #00ffff;             /* Cyan Accent */
    --research-accent-blue: #0080ff;        /* Blue Accent */
    --research-light: #f8fafc;              /* Light Background */
    --research-dark: #1a202c;               /* Dark Text */
    --research-text-light: #F5F5F5;         /* Light Text */
    --research-text-muted: #CCCCCC;         /* Muted Text */

    /* ASIMOV Brand Colors - Blue Theme */
    --asimov-blue: #2D7FF9;                 /* Primary Blue for ASIMOV logo */
    --asimov-blue-dark: #1C57B5;            /* Darker Blue */

    /* Gradients */
    --research-gradient-primary: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    --research-gradient-light: linear-gradient(135deg, #f8fafc 0%, #e6f0ff 100%);
    --research-gradient-accent: linear-gradient(135deg, #00ffff 0%, #0080ff 100%);
}

/* Mobile menu styles for research page */
.mobile-menu {
    position: fixed;
    top: 80px;
    left: 0;
    right: 0;
    background: white;
    padding: 1rem;
    border-bottom: 1px solid var(--neutral-200);
    transform: translateY(-100%);
    transition: transform 0.3s ease;
    z-index: 999;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.mobile-menu.active {
    transform: translateY(0);
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    color: var(--neutral-700);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.mobile-nav-link:hover {
    background: var(--primary-light);
    color: var(--primary-color);
}

.mobile-nav-link i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.mobile-menu-button {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
}

.mobile-menu-button span {
    width: 25px;
    height: 3px;
    background: var(--neutral-700);
    border-radius: 2px;
    transition: all 0.3s ease;
}

.mobile-menu-button.active span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-button.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-button.active span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Research Hero Section Enhancements */
.research-hero {
    background: var(--research-gradient-primary);
    position: relative;
    overflow: hidden;
}

.research-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="research-grid" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(0,255,255,0.2)"/><path d="M 0 10 L 20 10 M 10 0 L 10 20" stroke="rgba(0,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23research-grid)"/></svg>');
    opacity: 0.3;
}

.research-hero .hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: 3rem;
    max-width: 600px;
    opacity: 1;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    color: white;
    font-weight: 400;
}

/* Product Hero Section */
.product-hero {
    background: var(--research-light);
    padding: 6rem 0;
}

.product-hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

/* Quantum Interface Enhancements */
.quantum-interface {
    padding: 2rem;
    background: white;
    border-radius: 16px;
    border: 1px solid var(--research-accent);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.1);
}

.quantum-input,
.quantum-output {
    background: var(--research-light);
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 1rem;
}

.quantum-input {
    border-left: 4px solid var(--research-accent);
}

.quantum-output {
    border-left: 4px solid var(--research-accent-blue);
}

.quantum-arrow {
    text-align: center;
    color: var(--research-accent);
    font-size: 1.5rem;
    margin: 1rem 0;
}

.code-preview {
    background: #1a1a2e;
    color: var(--research-accent);
    padding: 1rem;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.code-line {
    display: block;
    margin-bottom: 0.5rem;
}

/* Pricing Section Enhancements */
.pricing-section {
    background: var(--research-gradient-light);
    padding: 6rem 0;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 3rem;
}

.pricing-card {
    background: white;
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 10px 30px rgba(26, 26, 46, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 255, 255, 0.2);
}

.pricing-card.featured {
    border: 2px solid var(--research-accent);
    transform: scale(1.05);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.pricing-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--research-gradient-accent);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Products Grid Section */
.products-grid-section {
    background: white;
    padding: 6rem 0;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.product-card {
    background: white;
    border: 1px solid var(--neutral-200);
    border-radius: 16px;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 255, 255, 0.15);
    border-color: var(--research-accent);
}

.product-icon {
    width: 60px;
    height: 60px;
    background: var(--research-gradient-primary);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.product-icon i {
    font-size: 1.5rem;
    color: var(--research-accent);
}

/* CTA Section */
.cta-section {
    background: var(--research-gradient-primary);
    color: white;
    padding: 6rem 0;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Button Styles */
.button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    text-align: center;
    justify-content: center;
}

.button-primary {
    background: var(--research-accent);
    color: var(--research-primary);
    box-shadow: 0 4px 15px rgba(0, 255, 255, 0.3);
}

.button-primary:hover {
    background: var(--research-accent-blue);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 128, 255, 0.4);
}

.button-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.button-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.button-outline {
    background: transparent;
    color: var(--research-accent);
    border: 2px solid var(--research-accent);
}

.button-outline:hover {
    background: var(--research-accent);
    color: var(--research-primary);
    border-color: var(--research-accent);
}

.button i {
    font-size: 1.1rem;
}

/* Pricing Section Button Overrides */
.pricing-card .button-outline {
    background: transparent;
    color: var(--research-accent);
    border: 2px solid var(--research-accent);
}

.pricing-card .button-outline:hover {
    background: var(--research-accent);
    color: var(--research-primary);
    border-color: var(--research-accent);
}

.pricing-card .button-primary {
    background: var(--research-accent);
    color: var(--research-primary);
    box-shadow: 0 4px 15px rgba(0, 255, 255, 0.3);
}

.pricing-card .button-primary:hover {
    background: var(--research-accent-blue);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 128, 255, 0.4);
}

/* Responsividade */
@media (max-width: 1200px) {
    .pricing-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }

    .product-hero-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
}

@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .mobile-menu-button {
        display: flex;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .research-hero {
        padding: 6rem 0 4rem;
    }

    .research-hero .hero-title {
        font-size: 2.5rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .quantum-interface {
        padding: 1rem;
    }

    .code-line {
        font-size: 0.7rem;
    }
}
