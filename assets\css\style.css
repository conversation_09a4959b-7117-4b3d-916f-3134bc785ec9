/* Reset e estilos base */
*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

:root {
    /* Sistema de cores - Nova paleta mais clara e profissional */
    --primary-color: #2D7FF9; /* Azul mais vibrante e profissional */
    --primary-dark: #1C57B5; /* Azul escuro mais suave */
    --primary-light: #E6F0FF; /* Azul muito claro para fundos */
    --secondary-color: #F8FAFC; /* Cinza muito claro para fundos secundários */
    --accent-color: #00C2FF; /* Azul turquesa para destaques */

    /* Cores neutras - Mais claras */
    --neutral-50: #FFFFFF;
    --neutral-100: #F9FAFB;
    --neutral-200: #F1F5F9;
    --neutral-300: #E2E8F0;
    --neutral-400: #CBD5E1;
    --neutral-500: #94A3B8;
    --neutral-600: #64748B;
    --neutral-700: #475569;
    --neutral-800: #334155;
    --neutral-900: #1E293B;

    /* Cores de texto */
    --text-dark: var(--neutral-800);
    --text-muted: var(--neutral-600);
    --text-light: white;

    /* Cores de fundo */
    --bg-color: var(--neutral-50);
    --bg-card: white;

    /* Cores específicas */
    --light-blue: rgba(45, 127, 249, 0.1);
    --dark-blue: var(--primary-dark);

    /* Cores para componentes */
    --text-primary: var(--neutral-800);
    --text-secondary: var(--neutral-600);
    --border-light: var(--neutral-200);
    --tag-bg: var(--neutral-100);
    --tag-text: var(--neutral-700);
    --tech-bg: var(--primary-light);
    --tech-text: var(--primary-dark);

    /* Gradientes */
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    --gradient-dark: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    --gradient-light: linear-gradient(135deg, var(--neutral-50), var(--primary-light));

    /* Sistema de espaçamento */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;
    --space-32: 8rem;
    --space-40: 10rem;
    --space-48: 12rem;
    --space-56: 14rem;
    --space-64: 16rem;

    /* Sistema de tipografia */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;
    --font-size-7xl: 4.5rem;
    --font-size-8xl: 6rem;
    --font-size-9xl: 8rem;

    --line-height-none: 1;
    --line-height-tight: 1.25;
    --line-height-snug: 1.375;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.625;
    --line-height-loose: 2;

    /* Sistema de bordas */
    --radius-sm: 0.125rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;

    /* Sistema de sombras */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

    /* Sistema de transições */
    --transition-fast: 150ms;
    --transition-normal: 300ms;
    --transition-slow: 500ms;
    --transition: all 0.3s ease;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    color: var(--neutral-800);
    background-color: var(--bg-color);
    line-height: var(--line-height-normal);
    overflow-x: hidden;
}

/* Navegação */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 80px;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(8px);
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.navbar.scrolled {
    height: 70px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.navbar-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
}

.nav-logo {
    display: flex;
    align-items: center;
}

.nav-logo .logo-link {
    display: flex;
    align-items: center;
    text-decoration: none;
}

.nav-logo img {
    height: 50px;
    margin-right: 1rem;
}

.logo-text {
    display: flex;
    flex-direction: column;
}

.logo-primary {
    font-size: var(--font-size-xl);
    font-weight: 800;
    color: var(--primary-color);
    line-height: 1.1;
}

.logo-secondary {
    font-size: var(--font-size-xs);
    color: var(--neutral-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.nav-links {
    display: flex;
    align-items: center;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    color: var(--neutral-700);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link.active {
    color: var(--primary-color);
    font-weight: 600;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: var(--radius-full);
}

.nav-link i {
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

.nav-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--gradient-primary);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.nav-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(124, 58, 237, 0.25);
}

/* Menu mobile moderno */
.mobile-menu {
    position: fixed;
    top: 4rem;
    left: 0;
    right: 0;
    background: white;
    padding: var(--space-4);
    border-bottom: 1px solid var(--neutral-200);
    transform: translateY(-100%);
    transition: transform var(--transition-normal);
}

.mobile-menu.active {
    transform: translateY(0);
}

.mobile-menu-link {
    display: block;
    padding: var(--space-4);
    color: var(--neutral-700);
    text-decoration: none;
    font-weight: 500;
    transition: background var(--transition-fast);
}

.mobile-menu-link:hover {
    background: var(--neutral-100);
}

.cta-button {
    position: relative;
    background: var(--gradient-primary);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    overflow: hidden;
    text-decoration: none;
    color: white;
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Hero Section */
.hero {
    padding-top: 120px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, var(--neutral-50) 0%, var(--primary-light) 100%);
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%232D7FF9' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2V6h4V4H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E"),
        radial-gradient(circle at 20% 20%, rgba(45, 127, 249, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(0, 194, 255, 0.08) 0%, transparent 50%);
    z-index: 0;
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.hero-title {
    font-size: 4rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    color: var(--primary-dark);
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.hero-title span {
    color: var(--primary-color);
    position: relative;
    display: inline-block;
}

.hero-title span::after {
    content: '';
    position: absolute;
    bottom: 5px;
    left: 0;
    width: 100%;
    height: 6px;
    background-color: var(--accent-color);
    opacity: 0.3;
    z-index: -1;
}

.hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: var(--neutral-700);
}

.hero-cta {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Seções de conteúdo */
.section {
    padding: var(--space-4) 0;
    margin: 0;
    position: relative;
}

/* Adicionar divisores entre seções */
.section:not(:last-child)::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: var(--neutral-200);
    opacity: 0.5;
}

/* Títulos de seção */
.section-title {
    margin-bottom: var(--space-2);
    position: relative;
    display: inline-block;
    left: 50%;
    transform: translateX(-50%);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 2px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
}

.section-subtitle {
    margin-bottom: var(--space-4);
    color: var(--neutral-600);
    text-align: center;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-6);
    margin-top: var(--space-8);
}

.service-card {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    position: relative;
    overflow: hidden;
    border: 1px solid var(--neutral-200);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.02);
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 60px;
    background: var(--gradient-primary);
    transform: scaleY(0);
    transform-origin: top;
    transition: transform 0.4s ease;
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--neutral-300);
}

.service-card:hover::before {
    transform: scaleY(1);
}

.service-card i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    display: inline-block;
}

.service-card h3 {
    color: var(--text-dark);
    font-size: 1.5rem;
    margin: 1rem 0;
    font-weight: 700;
}

.service-card p {
    color: var(--text-muted);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.tech-stack {
    display: flex;
    gap: 0.5rem;
    margin-top: 1.5rem;
    flex-wrap: wrap;
}

.tech-stack span {
    background: var(--primary-light);
    color: var(--primary-dark);
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
}

.service-features {


/* Seção de Produtos */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-8);
}

.product-card {
    background: white;
    border-radius: var(--radius-xl);
    overflow: hidden;
    border: 1px solid var(--neutral-200);
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-light);
}

.product-image {
    position: relative;
    height: 220px;
    overflow: hidden;
    background-color: var(--neutral-100);
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-content {
    padding: var(--space-6);
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.product-content h3 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--space-3);
    color: var(--neutral-900);
}

.product-content p {
    color: var(--neutral-600);
    margin-bottom: var(--space-6);
    line-height: 1.6;
    flex-grow: 1;
}

.product-actions {
    display: flex;
    gap: var(--space-3);
    margin-top: auto;
}

.product-actions .button {
    flex: 1;
    text-align: center;
    font-size: var(--font-size-sm);
    padding: var(--space-3) var(--space-4);
}

/* Seção de Produtos */
.products-section {
    background: linear-gradient(135deg, var(--neutral-50) 0%, var(--primary-light) 100%);
    position: relative;
    overflow: hidden;
    padding: var(--space-16) 0;
}

.products-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%232D7FF9' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2V6h4V4H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E"),
        radial-gradient(circle at 20% 20%, rgba(45, 127, 249, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(0, 194, 255, 0.08) 0%, transparent 50%);
    z-index: 0;
}

.products-header {
    text-align: center;
    position: relative;
    z-index: 1;
    margin-bottom: var(--space-8);
}

.products-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    background: var(--primary-light);
    color: var(--primary-dark);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 600;
    margin-bottom: var(--space-4);
}

.products-badge i {
    font-size: var(--font-size-base);
}

.products-showcase {
    position: relative;
    z-index: 1;
    margin-top: var(--space-8);
}

.products-showcase-content {
    display: flex;
    align-items: center;
    gap: var(--space-8);
    background: white;
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--neutral-200);
}

.products-showcase-text {
    flex: 1;
    padding: var(--space-8);
}

.products-showcase-text h3 {
    font-size: var(--font-size-2xl);
    color: var(--neutral-900);
    margin-bottom: var(--space-4);
    position: relative;
    display: inline-block;
}

.products-showcase-text h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
}

.products-showcase-text p {
    color: var(--neutral-600);
    line-height: 1.7;
    margin-bottom: var(--space-4);
    font-size: var(--font-size-lg);
}

.products-features {
    list-style: none;
    padding: 0;
    margin: var(--space-6) 0;
}

.products-features li {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-3);
    color: var(--neutral-700);
    font-weight: 500;
}

.products-features li i {
    color: var(--primary-color);
    font-size: var(--font-size-base);
}

.products-cta-buttons {
    display: flex;
    gap: var(--space-4);
    margin-top: var(--space-6);
}

.products-showcase-visual {
    flex: 1;
    position: relative;
    min-height: 450px;
    background: var(--gradient-primary);
}

.products-showcase-image {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    height: 80%;
    overflow: hidden;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    z-index: 2;
}

.products-showcase-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.products-showcase-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.decoration-item {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
}

.decoration-item.item-1 {
    width: 120px;
    height: 120px;
    top: 20%;
    left: 10%;
}

.decoration-item.item-2 {
    width: 80px;
    height: 80px;
    bottom: 20%;
    right: 15%;
}

.decoration-item.item-3 {
    width: 60px;
    height: 60px;
    top: 70%;
    left: 20%;
}

@media (max-width: 992px) {
    .products-showcase-content {
        flex-direction: column;
    }

    .products-showcase-visual {
        width: 100%;
        min-height: 350px;
        order: -1;
    }
}

@media (max-width: 768px) {
    .products-showcase-visual {
        min-height: 300px;
    }

    .products-cta-buttons {
        flex-direction: column;
    }

    .products-showcase-text {
        padding: var(--space-6);
    }

    .products-showcase-text h3 {
        font-size: var(--font-size-xl);
    }

    .products-showcase-text p {
        font-size: var(--font-size-base);
    }
}

/* Seção de Equipe */
.team-section {
    background-color: var(--neutral-50);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-8);
    padding: 0 var(--space-4);
}

.team-member {
    background: white;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    text-align: center;
}

.team-member:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.member-image {
    width: 100%;
    aspect-ratio: 1;
    overflow: hidden;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.team-member:hover .member-image img {
    transform: scale(1.05);
}

.member-info {
    padding: var(--space-6);
}

.member-info h3 {
    color: var(--text-dark);
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--space-2);
}

.member-role {
    display: inline-block;
    background: var(--primary-light);
    color: var(--primary-color);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

    margin: var(--space-4) 0;
    padding-left: 0;
}

.service-features li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    font-size: 0.95rem;
    color: var(--neutral-700);
}

.service-features li i {
    color: var(--primary-color);
    font-size: 1rem;
    margin-bottom: 0;
    background: none;
    -webkit-text-fill-color: currentColor;
}

.service-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: auto;
    padding-top: 1rem;
    text-decoration: none;
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    position: relative;
}

.service-link i {
    font-size: 0.875rem;
    transition: transform 0.3s ease;
}

.service-link:hover {
    color: var(--primary-dark);
}

.service-link:hover i {
    transform: translateX(4px);
}

/* Seção Sobre */
.bg-gradient {
    background: linear-gradient(135deg, var(--neutral-50) 30%, var(--primary-light) 100%);
    position: relative;
    overflow: hidden;
}

.bg-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%232D7FF9' fill-opacity='0.03' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
    z-index: 0;
}

.about-content {
    position: relative;
    z-index: 1;
    display: grid;
    grid-template-columns: 1fr 340px;
    gap: var(--space-8);
    align-items: start;
    margin-top: var(--space-8);
}

.about-text {
    display: flex;
    flex-direction: column;
    gap: var(--space-8);
    background: white;
    padding: var(--space-8);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--neutral-200);
}

.about-mission {
    margin-bottom: var(--space-6);
}

.about-mission h3,
.about-expertise h3,
.about-approach h3 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.5rem;
    margin-bottom: var(--space-4);
    color: var(--text-dark);
    font-weight: 700;
}

.about-mission h3 i,
.about-expertise h3 i,
.about-approach h3 i {
    color: white;
    background: var(--gradient-primary);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.about-mission p {
    color: var(--text-muted);
    line-height: 1.7;
    font-size: 1rem;
}

.expertise-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-4);
    margin-top: var(--space-4);
}

.expertise-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-4);
    background: var(--neutral-100);
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
    border: 1px solid var(--neutral-200);
}

.expertise-item:hover {
    background: var(--primary-light);
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.expertise-item i {
    font-size: 1.25rem;
    color: var(--primary-color);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.expertise-content {
    display: flex;
    flex-direction: column;
}

.expertise-content span {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.expertise-content small {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.approach-steps {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.step {
    display: flex;
    align-items: flex-start;
    gap: var(--space-4);
    padding: var(--space-4);
    background: var(--neutral-100);
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
    border-left: 3px solid var(--neutral-300);
}

.step:hover {
    background: var(--primary-light);
    border-left-color: var(--primary-color);
}

.step-number {
    font-size: 2rem;
    font-weight: 800;
    color: var(--primary-color);
    line-height: 1;
    opacity: 0.3;
    width: 40px;
    text-align: center;
}

.step-content h4 {
    font-size: 1.125rem;
    margin-bottom: 0.25rem;
}

.step-content p {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.about-certifications {
    margin-top: var(--space-12);
    padding-top: var(--space-8);
    border-top: 1px solid var(--neutral-200);
}

.certification-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
    margin-top: var(--space-4);
}

.certification-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    background: white;
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.certification-item i {
    font-size: 2rem;
    color: var(--primary-color);
}

.certification-item span {
    font-weight: 600;
    color: var(--neutral-700);
}

.about-cta {
    margin-top: var(--space-12);
    text-align: center;
    padding-top: var(--space-8);
    border-top: 1px solid var(--neutral-200);
}

.about-cta .button {
    background: var(--gradient-primary);
    color: white;
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: var(--radius-lg);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.about-cta .button i {
    font-size: 1rem;
}

/* Seção de Portfólio com Carrossel */
.portfolio-section {
    padding-top: calc(80px + var(--space-12));
    min-height: 100vh;
    background: var(--gradient-dark);
    position: relative;
    overflow: hidden;
}

.portfolio-container {
    position: relative;
    padding: 2rem 0;
}

.swiper-slide {
    height: auto;
    padding: 1rem;
}

.project-card {
    background: white;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    height: 100%;
    transition: all 0.3s ease;
    border: 1px solid var(--neutral-200);
}

.project-image {
    position: relative;
    padding-top: 60%;
    overflow: hidden;
}

.project-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.project-content {
    padding: 1.5rem;
}

/* Casos de uso melhorados */
.cases-section {
    background: var(--gradient-light);
    padding: var(--space-16) 0;
}

.cases-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-10);
}

.case-card {
    background: white;
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    position: relative;
    border: 1px solid var(--neutral-200);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.case-card .external-link {
    display: block;
    text-decoration: none;
    color: inherit;
    height: 100%;
    width: 100%;
}

.case-card .external-link:focus {
    outline: none;
}

.case-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.case-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-5);
    position: relative;
    border-bottom: 1px solid var(--neutral-200);
}

.case-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    opacity: 0.8;
}

.case-company {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.case-logo {
    width: 60px;
    height: 60px;
    object-fit: contain;
    border-radius: var(--radius-lg);
    padding: var(--space-2);
    background: white;
    border: 1px solid var(--neutral-200);
    transition: all 0.3s ease;
    filter: grayscale(0.3);
}

.case-logo[alt="Sem Fronteiras Logo"] {
    padding: var(--space-1);
}

.case-card:hover .case-logo {
    filter: grayscale(0);
    transform: scale(1.05);
}

.case-company-info {
    display: flex;
    flex-direction: column;
}

.case-company-info h3 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0 0 0.25rem;
}

.case-company-info span {
    font-size: 0.875rem;
    color: var(--text-muted);
    font-weight: 500;
}

.case-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    background: var(--primary-light);
    color: var(--primary-dark);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: 600;
}

.case-content {
    padding: var(--space-5);
}

.case-content h4 {
    font-size: 1.25rem;
    color: var(--text-dark);
    margin-bottom: var(--space-3);
    font-weight: 700;
}

.case-content p {
    color: var(--text-muted);
    margin-bottom: var(--space-4);
    font-size: 0.95rem;
    line-height: 1.6;
}

.case-results {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-3);
    margin: var(--space-5) 0;
}

.result-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.result-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-light);
    color: var(--primary-color);
}

.result-icon i {
    font-size: 1.2rem;
}

.result-info {
    flex: 1;
}

.result-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--primary-dark);
    line-height: 1.2;
}

.result-label {
    display: block;
    font-size: 0.75rem;
    color: var(--text-muted);
    line-height: 1.4;
}

.case-features {
    margin-top: var(--space-4);
    margin-bottom: var(--space-4);
}

.feature-list {
    list-style: none;
    padding-left: 0;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

.feature-list li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--neutral-700);
}

.feature-list i {
    color: var(--primary-color);
}

.solution-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: var(--space-4);
}

.solution-tags span {
    background: var(--neutral-200);
    color: var(--neutral-700);
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 600;
    letter-spacing: 0.02em;
}

/* Customização do Swiper */
.swiper-button-prev,
.swiper-button-next {
    color: var(--primary-color);
    background: var(--bg-card);
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    box-shadow: var(--box-shadow);
}

.swiper-button-prev:after,
.swiper-button-next:after {
    font-size: 1.25rem;
}

.swiper-pagination-bullet {
    background: var(--primary-color);
    opacity: 0.5;
}

.swiper-pagination-bullet-active {
    opacity: 1;
    background: var(--primary-color);
    transform: scale(1.2);
}

/* Formulário de Contato */
.contact-container {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    margin-top: 2rem;
}

.contact-info {
    padding: 2rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.contact-form {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-lg);
}

.form-group {
    position: relative;
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
    font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 1rem;
    border: 2px solid var(--neutral-200);
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
    font-size: var(--font-size-base);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(124, 58, 237, 0.1);
    outline: none;
}

.btn-submit {
    width: 100%;
    padding: 1rem;
    font-weight: 600;
    font-size: 1.1rem;
    border-radius: 8px;
    background: var(--gradient-primary);
    transition: var(--transition);
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(107, 70, 193, 0.2);
}

/* Footer Styles */
.footer {
    background-color: #0a1930;
    color: #fff;
    padding: 4rem 0 2rem;
    position: relative;
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-bottom: 3rem;
}

@media (max-width: 992px) {
    .footer-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .footer-grid {
        grid-template-columns: 1fr;
    }
}

.footer-column {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.footer-logo {
    width: 40px;
    height: 40px;
    object-fit: contain;
}

.footer-brand h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #fff;
    margin: 0;
}

.footer-description {
    color: #a3b3c9;
    line-height: 1.6;
    margin: 0;
}

.footer-social {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.footer-social .social-link {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    transition: all 0.3s ease;
}

.footer-social .social-link:hover {
    background-color: #0066ff;
    transform: translateY(-3px);
}

.footer-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #fff;
    margin: 0 0 1rem;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.footer-links a {
    color: #a3b3c9;
    text-decoration: none;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-links a:hover {
    color: #0066ff;
}

.footer-contact {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.footer-contact li {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    color: #a3b3c9;
}

.footer-contact li i {
    width: 20px;
    color: #0066ff;
}

.footer-bottom {
    padding-top: 2rem;
    margin-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

@media (max-width: 768px) {
    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }
}

.footer-copyright {
    color: #a3b3c9;
}

.footer-legal {
    display: flex;
    gap: 2rem;
}

.footer-legal a {
    color: #a3b3c9;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-legal a:hover {
    color: #0066ff;
}

/* Ajustes de responsividade */
@media (max-width: 1200px) {
    :root {
        --section-spacing: 5rem;
    }

    .container {
        max-width: 90%;
    }

    .hero-title {
        font-size: 3.5rem;
    }
}

@media (max-width: 1024px) {
    :root {
        --section-spacing: 4rem;
        --card-spacing: 1.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: var(--space-8);
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: var(--space-8);
    }

    .expertise-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-6);
    }

    .hero-title {
        font-size: 3rem;
    }

    .service-card {
        padding: var(--space-6);
    }

    .about-content {
        grid-template-columns: 1fr;
    }

    .about-certifications {
        margin-top: var(--space-8);
        padding-top: var(--space-6);
    }

    .about-cta {
        margin-top: var(--space-8);
        padding-top: var(--space-6);
    }
}

@media (max-width: 768px) {
    :root {
        --section-spacing: 3rem;
    }

    .navbar {
        height: 70px;
    }

    .nav-links {
        display: none;
    }

    .mobile-menu {
        display: block;
    }

    .hero {
        padding-top: calc(70px + var(--space-8));
        min-height: auto;
    }

    .hero-title {
        font-size: 2.5rem;
        text-align: center;
    }

    .hero-description {
        text-align: center;
        font-size: 1.1rem;
    }

    .hero-cta {
        justify-content: center;
    }

    .container {
        padding: 0 var(--space-4);
    }

    .section {
        padding: var(--space-8) 0;
        margin: var(--space-8) 0;
    }

    .section:not(:last-child)::after {
        bottom: calc(-1 * var(--space-8));
    }

    .section-title {
        margin-bottom: var(--space-4);
    }

    .section-subtitle {
        margin-bottom: var(--space-6);
    }

    .services-grid {
        gap: var(--space-4);
        margin-top: var(--space-6);
    }

    .service-card {
        padding: var(--space-4);
    }

    .service-features {
        margin: var(--space-3) 0;
    }

    .service-link {
        margin-top: var(--space-3);
        padding-top: var(--space-3);
    }

    .contact-wrapper {
        gap: var(--space-4);
        margin-top: var(--space-4);
    }

    .contact-form {
        padding: var(--space-4);
    }

    .form-grid {
        gap: var(--space-3);
    }

    .submit-button {
        margin-top: var(--space-3);
    }

    .testimonials-grid {
        gap: var(--space-4);
        margin: var(--space-6) 0 var(--space-12);
    }

    .testimonial-card {
        padding: var(--space-4);
    }

    .testimonial-header {
        margin-bottom: var(--space-3);
    }

    .testimonial-content {
        padding: var(--space-3) 0;
    }

    .case-company {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-4);
    }

    .case-company-info {
        padding-right: 0;
    }

    .case-logo {
        width: 140px;
        height: 140px;
        min-width: 140px;
    }
}

@media (max-width: 480px) {
    :root {
        --section-spacing: 2.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .service-card,
    .case-card,
    .testimonial-card {
        padding: var(--space-4);
    }

    .stat-value {
        font-size: 2rem;
    }

    .contact-form {
        padding: var(--space-6);
    }

    .section {
        padding: var(--space-6) 0;
        margin: var(--space-6) 0;
    }

    .section:not(:last-child)::after {
        bottom: calc(-1 * var(--space-6));
    }

    .services-grid {
        gap: var(--space-3);
        margin-top: var(--space-4);
    }

    .service-card {
        padding: var(--space-3);
    }

    .service-features {
        margin: var(--space-2) 0;
    }

    .service-link {
        margin-top: var(--space-2);
        padding-top: var(--space-2);
    }

    .contact-wrapper {
        gap: var(--space-3);
        margin-top: var(--space-3);
    }

    .contact-form {
        padding: var(--space-3);
    }

    .form-grid {
        gap: var(--space-2);
    }

    .submit-button {
        margin-top: var(--space-2);
    }

    .testimonials-grid {
        gap: var(--space-3);
        margin: var(--space-4) 0 var(--space-8);
    }

    .testimonial-card {
        padding: var(--space-3);
    }

    .testimonial-header {
        margin-bottom: var(--space-2);
    }

    .testimonial-content {
        padding: var(--space-2) 0;
    }
}

/* Animações */
@keyframes float {
    from {
        transform: translateY(0) rotate(0deg);
    }
    to {
        transform: translateY(-100%) rotate(10deg);
    }
}

@keyframes shimmer {
    0% {
        background-position: -1000px 0;
    }
    100% {
        background-position: 1000px 0;
    }
}

.animate-shimmer {
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    background-size: 1000px 100%;
    animation: shimmer 2s infinite linear;
}

/* Melhorar scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-color);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* Melhorias de acessibilidade */
:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Container responsivo */
.container {
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

@media (min-width: 640px) {
    .container {
        padding: 0 var(--space-6);
    }
}

/* Tipografia refinada */
h1, h2, h3, h4, h5, h6 {
    line-height: var(--line-height-tight);
    font-weight: 700;
    color: var(--neutral-900);
}

h1 {
    font-size: var(--font-size-4xl);
    letter-spacing: -0.025em;
}

@media (min-width: 768px) {
    h1 {
        font-size: var(--font-size-5xl);
    }
}

/* Botões modernos */
.button {
    position: relative;
    padding: 1rem 2rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    letter-spacing: 0.025em;
    overflow: hidden;
    transition: all 0.3s ease;
    text-decoration: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
}

.button-primary {
    background: var(--gradient-primary);
    color: white;
    border: none;
    text-decoration: none;
}

.button-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: 0.5s;
}

.button-primary:hover::before {
    left: 100%;
}

.button-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    text-decoration: none;
}

.button-secondary:hover {
    background: var(--primary-color);
    color: white;
}

.nav-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--gradient-primary);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.nav-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(124, 58, 237, 0.25);
}

.cta-button {
    position: relative;
    background: var(--gradient-primary);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    overflow: hidden;
    text-decoration: none;
    color: white;
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.about-cta .button {
    background: var(--gradient-primary);
    color: white;
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: var(--radius-lg);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.about-cta .button i {
    font-size: 1rem;
}

/* Links em geral */
a {
    text-decoration: none;
}

/* Cards modernos */
.card {
    background: white;
    border-radius: var(--radius-xl);
    overflow: hidden;
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

/* Formulários modernos */
.form-group {
    margin-bottom: var(--space-4);
}

.form-label {
    display: block;
    margin-bottom: var(--space-2);
    color: var(--neutral-700);
    font-weight: 500;
}

.form-input {
    width: 100%;
    padding: var(--space-3);
    border: 2px solid var(--neutral-300);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
}

.form-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
    outline: none;
}

/* Sistema de grid moderno */
.grid {
    display: grid;
    gap: var(--space-6);
}

.grid-2 {
    grid-template-columns: repeat(1, 1fr);
}

.grid-3 {
    grid-template-columns: repeat(1, 1fr);
}

.grid-4 {
    grid-template-columns: repeat(1, 1fr);
}

@media (min-width: 640px) {
    .grid-2 {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 768px) {
    .grid-3 {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1024px) {
    .grid-4 {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Utilitários responsivos */
.hide-mobile {
    display: none;
}

@media (min-width: 768px) {
    .hide-mobile {
        display: block;
    }

    .hide-desktop {
        display: none;
    }
}

/* Melhorias de acessibilidade */
.skip-link {
    position: absolute;
    top: -40px;
    left: 0;
    background: var(--primary-color);
    color: white;
    padding: 8px;
    z-index: 100;
}

.skip-link:focus {
    top: 0;
}

/* Animações suaves */
@keyframes fadeUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-up {
    animation: fadeUp 0.6s ease forwards;
}

/* Loading states */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Ajuste do container dentro das seções */
.section-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 var(--space-4);
    position: relative;
    z-index: 2;
}

/* Ajuste específico para cada seção */
#servicos {
    margin-top: 0;
    padding-top: var(--space-16);
}

#portfolio {
    padding: var(--space-16) 0;
}

#cases {
    margin: var(--space-16) 0;
}

#sobre {
    padding: var(--space-16) 0;
}

#contato {
    margin-bottom: 0;
    padding-bottom: var(--space-16);
}

/* Animações */
.case-card {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease forwards;
}

.case-card:nth-child(2) {
    animation-delay: 0.2s;
}

.case-card:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Seção de Dados de Mercado */
.market-data-section {
    background: var(--gradient-light);
    position: relative;
    overflow: hidden;
}

.market-data-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(45, 127, 249, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(0, 194, 255, 0.1) 0%, transparent 50%);
}

.market-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.market-stat-card {
    background: white;
    border-radius: var(--radius-xl);
    padding: 2rem;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--neutral-200);
    transition: all 0.3s ease;
}

.market-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.stat-icon {
    width: 48px;
    height: 48px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.stat-icon i {
    font-size: 1.5rem;
    color: white;
}

.stat-content {
    position: relative;
    z-index: 1;
}

.stat-header {
    display: flex;
    align-items: baseline;
    gap: 0.25rem;
    margin-bottom: 1rem;
}

.stat-value {
    font-size: 3rem;
    font-weight: 800;
    line-height: 1;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease forwards 0.3s;
}

.stat-symbol {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

.stat-description {
    color: var(--neutral-700);
    font-size: 1rem;
    line-height: 1.5;
    margin-bottom: 0.5rem;
}

.stat-source {
    font-size: 0.875rem;
    color: var(--neutral-500);
}

/* Elementos visuais específicos */
.stat-progress {
    margin-top: 1.5rem;
    height: 6px;
    background: var(--neutral-100);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    transform: scaleX(0);
    transform-origin: left;
    animation: progressBar 1.5s ease forwards 0.5s;
}

.stat-trend {
    margin-top: 1.5rem;
    height: 30px;
}

.trend-line {
    width: 100%;
    height: 100%;
    fill: none;
    stroke: url(#gradient);
    stroke-width: 2;
    stroke-linecap: round;
    opacity: 0;
    animation: drawLine 1.5s ease forwards 0.5s;
}

.stat-chart {
    margin-top: 1.5rem;
    height: 100px;
    display: flex;
    align-items: flex-end;
}

.chart-bar {
    width: 100%;
    background: var(--gradient-primary);
    height: 0;
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    animation: growBar 1.5s ease forwards 0.5s;
}

.stat-circle {
    margin-top: 1.5rem;
    width: 80px;
    height: 80px;
}

.stat-circle svg {
    transform: rotate(-90deg);
    stroke: var(--primary-color);
    stroke-width: 2.5;
    fill: none;
    stroke-dasharray: 100;
    stroke-dashoffset: 100;
    animation: drawCircle 1.5s ease forwards 0.5s;
}

/* Animações */
@keyframes progressBar {
    to {
        transform: scaleX(1);
    }
}

@keyframes drawLine {
    0% {
        opacity: 0;
        stroke-dasharray: 100;
        stroke-dashoffset: 100;
    }
    100% {
        opacity: 1;
        stroke-dasharray: 100;
        stroke-dashoffset: 0;
    }
}

@keyframes growBar {
    to {
        height: 100%;
    }
}

@keyframes drawCircle {
    to {
        stroke-dashoffset: 25;
    }
}

/* Responsividade */
@media (max-width: 768px) {
    .market-stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-value {
        font-size: 2.5rem;
    }

    .stat-description {
        font-size: 0.95rem;
    }
}

/* Estilos para a seção de depoimentos */
.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-6);
    margin: var(--space-8) 0 var(--space-16);
}

.testimonial-card {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
    border: 1px solid var(--neutral-200);
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.testimonial-header {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    margin-bottom: var(--space-4);
}

.testimonial-avatar {
    width: 64px;
    height: 64px;
    border-radius: var(--radius-full);
    overflow: hidden;
    border: 3px solid var(--primary-color);
}

.testimonial-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.testimonial-info h4 {
    color: var(--neutral-900);
    margin-bottom: var(--space-1);
}

.testimonial-info p {
    color: var(--neutral-600);
    font-size: var(--font-size-sm);
}

.testimonial-content {
    position: relative;
    padding: var(--space-4) 0;
}

.testimonial-content::before {
    content: '"';
    position: absolute;
    top: 0;
    left: -10px;
    font-size: 4rem;
    color: var(--primary-color);
    opacity: 0.2;
    line-height: 1;
}

.testimonial-content p {
    color: var(--neutral-700);
    line-height: 1.6;
    font-style: italic;
}

.testimonial-rating {
    margin-top: var(--space-4);
    color: #FFB800;
}

/* Estilos para a seção de contato */
.contact-section {
    background: var(--gradient-light);
    padding-bottom: var(--space-16);
    margin-bottom: var(--space-12);
}

.contact-wrapper {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--space-6);
    margin-top: var(--space-6);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
}

.info-card {
    background: white;
    padding: var(--space-6);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    gap: var(--space-4);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.info-card:hover {
    transform: translateX(10px);
    box-shadow: var(--shadow-lg);
}

.info-icon {
    width: 48px;
    height: 48px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.info-content h3 {
    color: var(--neutral-900);
    margin-bottom: var(--space-1);
}

.info-content p {
    color: var(--neutral-600);
}

.social-links {
    display: flex;
    gap: var(--space-4);
    margin-top: var(--space-4);
    justify-content: center;
    align-items: center;
}

.social-link {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.social-link i {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.social-link.linkedin {
    background: #0077B5;
}

.social-link.github {
    background: #333;
}

.social-link.whatsapp {
    background: #25D366;
}

.social-link:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.contact-form {
    background: white;
    padding: var(--space-6);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
}

.form-header {
    margin-bottom: var(--space-6);
    text-align: center;
}

.form-header h3 {
    color: var(--neutral-900);
    margin-bottom: var(--space-2);
}

.form-header p {
    color: var(--neutral-600);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-4);
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    display: block;
    margin-bottom: var(--space-2);
    color: var(--neutral-700);
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--space-3);
    border: 2px solid var(--neutral-200);
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
}

.form-group textarea {
    height: 150px;
    resize: vertical;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(45, 127, 249, 0.1);
    outline: none;
}

.submit-button {
    width: 100%;
    margin-top: var(--space-4);
    padding: var(--space-4);
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    transition: all 0.3s ease;
    cursor: pointer;
}

.submit-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Responsividade */
@media (max-width: 768px) {
    .contact-wrapper {
        grid-template-columns: 1fr;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }
}

/* Estatísticas */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-12);
    margin-bottom: var(--space-12);
}

.stat-item {
    background: white;
    padding: var(--space-6);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid var(--neutral-200);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-4);
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-light);
}

.stat-item i {
    font-size: 2.5rem;
    color: var(--primary-color);
    background: var(--neutral-100);
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-full);
    margin-bottom: var(--space-4);
    transition: all 0.3s ease;
}

.stat-item:hover i {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.stat-number {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    color: var(--neutral-900);
    line-height: 1;
    margin-bottom: var(--space-2);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.stat-label {
    font-size: var(--font-size-lg);
    color: var(--neutral-600);
    font-weight: 500;
}

@media (max-width: 768px) {
    .stats-container {
        grid-template-columns: 1fr;
        gap: var(--space-6);
        margin-top: var(--space-8);
        margin-bottom: var(--space-8);
    }

    .stat-item {
        padding: var(--space-4);
    }

    .stat-item i {
        width: 60px;
        height: 60px;
        font-size: 2rem;
    }

    .stat-number {
        font-size: var(--font-size-3xl);
    }

    .stat-label {
        font-size: var(--font-size-base);
    }
}

/* Estilos da página de Portfólio */

/* Filtros do Portfólio */
.portfolio-filters {
    background: white;
    padding: calc(100px + var(--space-12)) 0 var(--space-12);
    border-bottom: 1px solid var(--neutral-200);
    margin-bottom: var(--space-16);
}

.filter-buttons {
    display: flex;
    justify-content: center;
    gap: var(--space-6);
    flex-wrap: wrap;
    padding: 0 var(--space-4);
}

.filter-btn {
    padding: var(--space-4) var(--space-8);
    border: 2px solid var(--neutral-200);
    border-radius: var(--radius-full);
    background: white;
    color: var(--neutral-700);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.filter-btn::after {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
    color: white;
    border-color: var(--primary-color);
}

.filter-btn:hover::after,
.filter-btn.active::after {
    opacity: 1;
}

.filter-btn:not(.active):hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Grid de Projetos */
.portfolio-grid {
    padding: var(--space-8) 0 var(--space-20);
    background: var(--neutral-50);
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-12);
    margin: var(--space-8) auto;
    max-width: 1400px;
    padding: 0 var(--space-4);
}

.project-card {
    background: white;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
    border: 1px solid var(--neutral-200);
    margin-bottom: var(--space-8);
}

.project-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.project-image {
    position: relative;
    padding-top: 60%;
    overflow: hidden;
}

.project-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.project-card:hover .project-image img {
    transform: scale(1.05);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: flex-end;
    padding: var(--space-4);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-tags {
    display: flex;
    gap: var(--space-2);
    flex-wrap: wrap;
}

.project-tags span {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    backdrop-filter: blur(4px);
}

.project-content {
    padding: var(--space-8) var(--space-8) var(--space-12);
}

.project-content h3 {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--space-4);
    color: var(--neutral-900);
}

.project-content p {
    color: var(--neutral-600);
    line-height: 1.6;
    margin-bottom: var(--space-6);
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-3);
    margin-bottom: var(--space-6);
}

.project-tech span {
    background: var(--neutral-100);
    color: var(--primary-color);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.project-link {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.project-link:hover {
    color: var(--primary-dark);
    gap: var(--space-3);
}

.project-status {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    background: var(--neutral-100);
    border-radius: var(--radius-full);
    color: var(--neutral-600);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.project-status i {
    color: var(--primary-color);
    font-size: 1.1em;
}

.project-status span {
    color: var(--neutral-700);
}

/* CTA Section */
.portfolio-cta {
    background: var(--gradient-primary);
    padding: var(--space-20) 0;
    margin-top: var(--space-20);
    text-align: center;
    color: white;
}

.cta-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.cta-content h2 {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--space-8);
    color: white;
}

.cta-content p {
    font-size: var(--font-size-xl);
    margin-bottom: var(--space-12);
    opacity: 0.9;
    line-height: 1.6;
}

/* Responsividade */
@media (max-width: 768px) {
    .tech-icons {
        gap: var(--space-4);
    }

    .tech-icon i {
        font-size: 2rem;
    }

    .portfolio-filters {
        padding: var(--space-8) 0;
        margin-bottom: var(--space-12);
    }

    .filter-buttons {
        gap: var(--space-4);
    }

    .filter-btn {
        padding: var(--space-3) var(--space-6);
    }

    .projects-grid {
        gap: var(--space-8);
    }

    .portfolio-cta {
        padding: var(--space-16) 0;
        margin-top: var(--space-16);
    }
}

@media (max-width: 480px) {
    .project-content {
        padding: var(--space-6);
    }
}



/* Seção de Tecnologias */
.tech-stack-section {
    margin: var(--space-8) auto var(--space-12);
    max-width: 1000px;
    text-align: center;
}

.tech-stack-title {
    font-size: var(--font-size-xl);
    color: var(--neutral-200);
    margin-bottom: var(--space-6);
    font-weight: 600;
}

.tech-stack-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: var(--space-6);
    padding: 0 var(--space-4);
}

.tech-item {
    background: rgba(255, 255, 255, 0.05);
    padding: var(--space-4);
    border-radius: var(--radius-xl);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.tech-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--primary-light);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.tech-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-lg);
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.tech-item:hover .tech-icon {
    background: var(--gradient-primary);
    transform: scale(1.1) rotate(5deg);
}

.tech-icon i {
    font-size: 1.75rem;
    color: var(--primary-light);
    transition: all 0.3s ease;
}

.tech-item:hover .tech-icon i {
    color: white;
}

.tech-icon::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: 0.5s;
}

.tech-item:hover .tech-icon::after {
    left: 100%;
}

.tech-name {
    color: var(--neutral-200);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

@media (max-width: 768px) {
    .tech-stack-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--space-4);
    }

    .tech-icon {
        width: 40px;
        height: 40px;
    }

    .tech-icon i {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .tech-stack-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-3);
    }

    .tech-icon {
        width: 36px;
        height: 36px;
    }

    .tech-icon i {
        font-size: 1.25rem;
    }

    .tech-name {
        font-size: var(--font-size-xs);
    }
}

.login-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    margin-left: 0.75rem;
    border-radius: var(--radius-md);
    color: var(--primary-color);
    font-weight: 600;
    border: 1px solid var(--primary-color);
    transition: all 0.3s ease;
}

.login-link:hover {
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 4px 6px rgba(45, 127, 249, 0.2);
}

.login-link i {
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.login-link:hover i {
    transform: translateY(-2px);
}

/* Estilos para a página de serviços */
.services-detail {
    padding-top: 120px;
    padding-bottom: 80px;
}

.services-detail .section-title {
    margin-bottom: 40px;
    text-align: center;
    color: var(--primary-dark);
}

.service-item {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    margin-bottom: var(--space-8);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--neutral-200);
    transition: all 0.3s ease;
}

.service-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-light);
}

.service-item h2 {
    color: var(--primary-color);
    font-size: var(--font-size-2xl);
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-3);
    border-bottom: 1px solid var(--neutral-200);
}

.service-item ul {
    list-style: none;
    padding-left: var(--space-4);
}

.service-item ul li {
    position: relative;
    padding: var(--space-2) 0;
    padding-left: var(--space-6);
    color: var(--neutral-700);
}

.service-item ul li:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
}

.mobile-menu .login-link {
    margin: var(--space-4) auto;
    width: 90%;
    justify-content: center;
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-lg);
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.mobile-menu .login-link:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Footer Styles */
.footer {
    background-color: #0a1930;
    color: #fff;
    padding: 4rem 0 2rem;
    position: relative;
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-bottom: 3rem;
}

@media (max-width: 992px) {
    .footer-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .footer-grid {
        grid-template-columns: 1fr;
    }
}

.footer-column {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.footer-logo {
    width: 40px;
    height: 40px;
    object-fit: contain;
}

.footer-brand h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #fff;
    margin: 0;
}

.footer-description {
    color: #a3b3c9;
    line-height: 1.6;
    margin: 0;
}

.footer-social {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.footer-social .social-link {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    transition: all 0.3s ease;
}

.footer-social .social-link:hover {
    background-color: #0066ff;
    transform: translateY(-3px);
}

.footer-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #fff;
    margin: 0 0 1rem;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.footer-links a {
    color: #a3b3c9;
    text-decoration: none;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-links a:hover {
    color: #0066ff;
}

.footer-contact {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.footer-contact li {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    color: #a3b3c9;
}

.footer-contact li i {
    width: 20px;
    color: #0066ff;
}

.footer-bottom {
    padding-top: 2rem;
    margin-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

@media (max-width: 768px) {
    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }
}

.footer-copyright {
    color: #a3b3c9;
}

.footer-legal {
    display: flex;
    gap: 2rem;
}

.footer-legal a {
    color: #a3b3c9;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-legal a:hover {
    color: #0066ff;
}

/* ===== SEÇÃO DE VERTICAIS ===== */
.verticals-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
    align-items: start;
}

.vertical-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    height: fit-content;
    display: flex;
    flex-direction: column;
}

.vertical-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.vertical-card:hover::before {
    transform: scaleX(1);
}

.vertical-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.vertical-card.featured {
    border: 2px solid var(--primary-color);
    background: linear-gradient(135deg, rgba(0, 102, 255, 0.05) 0%, rgba(0, 82, 204, 0.05) 100%);
}

.vertical-card.featured::before {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.vertical-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    color: white;
}

.vertical-card.featured .vertical-icon {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.vertical-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #334155;
    margin-bottom: 1rem;
}

.vertical-card p {
    color: #64748b;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.vertical-products {
    margin-bottom: 1rem;
    flex-shrink: 0;
}

.product-count {
    display: inline-block;
    background: #f1f5f9;
    color: #475569;
    padding: 0.3rem 0.8rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.product-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    max-height: 80px;
    overflow: hidden;
    position: relative;
}

.product-list::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: linear-gradient(transparent, white);
    pointer-events: none;
}

.product-name {
    background: #e6f0ff;
    color: #1c57b5;
    padding: 0.3rem 0.6rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
}

.product-name.hero {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    font-weight: 600;
}

.vertical-market {
    margin-bottom: 1.5rem;
    flex-shrink: 0;
}

.market-info {
    color: #64748b;
    font-size: 0.9rem;
    font-style: italic;
}

.vertical-card .hero-badge {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    flex-shrink: 0;
}

.vertical-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #2d7ff9;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-top: auto;
    flex-shrink: 0;
}

.vertical-link:hover {
    gap: 0.75rem;
    color: #1c57b5;
}

.vertical-link i {
    transition: transform 0.3s ease;
}

.vertical-link:hover i {
    transform: translateX(4px);
}

/* Responsividade para verticais */
@media (max-width: 1200px) {
    .verticals-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    }
}

@media (max-width: 768px) {
    .verticals-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .vertical-card {
        padding: 1.5rem;
    }

    .vertical-card h3 {
        font-size: 1.3rem;
    }
}

/* ===== PROJETOS CUSTOMIZADOS ===== */
.custom-projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.custom-project-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    height: fit-content;
}

.custom-project-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: #2d7ff9;
}

.custom-project-card .project-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.custom-project-card .project-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #2d7ff9, #00c2ff);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.custom-project-card .project-status {
    padding: 0.3rem 0.8rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    background: #f1f5f9;
    color: #475569;
}

.custom-project-card .project-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: #334155;
    margin-bottom: 1rem;
}

.custom-project-card .project-description {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    flex-grow: 1;
}

.custom-project-card .project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.custom-project-card .tech-tag {
    background: #e6f0ff;
    color: #1c57b5;
    padding: 0.3rem 0.6rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
}

.custom-project-card .project-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    background: linear-gradient(135deg, #2d7ff9, #1c57b5);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-top: auto;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-size: 0.9rem;
}

.custom-project-card .project-link:hover {
    gap: 0.75rem;
    background: linear-gradient(135deg, #1c57b5, #0f3d82);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(45, 127, 249, 0.3);
}

.custom-project-card .project-link.disabled {
    color: white;
    background: linear-gradient(135deg, #94a3b8, #64748b);
    cursor: not-allowed;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-size: 0.9rem;
}

.custom-project-card .project-link.disabled:hover {
    gap: 0.5rem;
    background: linear-gradient(135deg, #94a3b8, #64748b);
    transform: none;
    box-shadow: none;
}

.custom-project-card .project-link i {
    transition: transform 0.3s ease;
}

.custom-project-card .project-link:hover i {
    transform: translateX(4px);
}

.custom-project-card .project-link.disabled:hover i {
    transform: none;
}

/* Responsividade projetos customizados */
@media (max-width: 1200px) {
    .custom-projects-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}

@media (max-width: 768px) {
    .custom-projects-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .custom-project-card {
        padding: 1.5rem;
    }
}

/* ===== ASIMOV HEALTH STYLES ===== */
.health-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8rem 0 6rem;
    position: relative;
    overflow: hidden;
}

.health-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.health-hero .container {
    position: relative;
    z-index: 2;
}

.health-hero .hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 2rem;
}

.health-hero .hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
}

.health-hero .hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: 3rem;
    max-width: 600px;
    opacity: 0.9;
}

.health-hero .hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.health-hero .stat-item {
    text-align: center;
}

.health-hero .stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #00ff88;
    margin-bottom: 0.5rem;
}

.health-hero .stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.health-hero .hero-cta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Product Hero específico para Health */
.product-hero {
    padding: 6rem 0;
    background: #f8fafc;
}

.product-hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.product-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.product-title {
    font-size: 3rem;
    font-weight: 800;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.product-subtitle {
    font-size: 1.25rem;
    color: #667eea;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.product-description {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #64748b;
    margin-bottom: 2rem;
}

.feature-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
    color: #334155;
}

.feature-item i {
    color: #00ff88;
    font-size: 1.1rem;
}

.product-cta {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.status-badge {
    padding: 0.3rem 0.8rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-badge.beta {
    background: #fef3c7;
    color: #92400e;
}

.status-badge.planning {
    background: #e0e7ff;
    color: #3730a3;
}

/* Product Preview */
.product-preview {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: 1px solid #e2e8f0;
}

.preview-header {
    background: #f1f5f9;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: 1px solid #e2e8f0;
}

.preview-controls {
    display: flex;
    gap: 0.5rem;
}

.preview-controls .control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #cbd5e1;
}

.preview-controls .control:first-child {
    background: #ef4444;
}

.preview-controls .control:nth-child(2) {
    background: #f59e0b;
}

.preview-controls .control:last-child {
    background: #10b981;
}

.preview-title {
    font-weight: 600;
    color: #64748b;
    font-size: 0.9rem;
}

.preview-content {
    padding: 2rem;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.dashboard-card {
    background: #f8fafc;
    padding: 1.5rem 1rem;
    border-radius: 12px;
    text-align: center;
    border: 1px solid #e2e8f0;
}

.dashboard-card h4 {
    font-size: 0.8rem;
    color: #64748b;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.dashboard-card .metric {
    font-size: 1.5rem;
    font-weight: 800;
    color: #667eea;
}

/* Responsividade Health */
@media (max-width: 1200px) {
    .product-hero-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
}

@media (max-width: 768px) {
    .health-hero {
        padding: 6rem 0 4rem;
    }

    .health-hero .hero-title {
        font-size: 2.5rem;
    }

    .health-hero .hero-stats {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1.5rem;
    }

    .health-hero .hero-cta {
        flex-direction: column;
        align-items: center;
    }

    .product-title {
        font-size: 2.5rem;
    }

    .product-cta {
        flex-direction: column;
        align-items: flex-start;
    }
}

/* ===== ASIMOV RESEARCH STYLES ===== */
.research-hero {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: white;
    padding: 8rem 0 6rem;
    position: relative;
    overflow: hidden;
}

.research-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="quantum-grid" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(0,255,255,0.3)"/><path d="M 0 10 L 20 10 M 10 0 L 10 20" stroke="rgba(0,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23quantum-grid)"/></svg>');
    opacity: 0.4;
}

.research-hero .container {
    position: relative;
    z-index: 2;
}

.research-hero .hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(0, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 2rem;
    border: 1px solid rgba(0, 255, 255, 0.3);
}

.research-hero .hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
}

.research-hero .highlight {
    background: linear-gradient(135deg, #00ffff, #0080ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.research-hero .hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: 3rem;
    max-width: 600px;
    opacity: 0.9;
}

.research-hero .hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.research-hero .stat-item {
    text-align: center;
}

.research-hero .stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #00ffff;
    margin-bottom: 0.5rem;
    text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

.research-hero .stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.research-hero .hero-cta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Quantum Interface Preview */
.quantum-interface {
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 12px;
}

.quantum-input {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #00ffff;
    margin-bottom: 1rem;
}

.quantum-input h4 {
    font-size: 0.9rem;
    color: #64748b;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.quantum-input p {
    font-style: italic;
    color: #334155;
    margin: 0;
}

.quantum-arrow {
    text-align: center;
    margin: 1rem 0;
    color: #00ffff;
    font-size: 1.5rem;
}

.quantum-output {
    background: #1e293b;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #0080ff;
}

.quantum-output h4 {
    font-size: 0.9rem;
    color: #94a3b8;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.code-preview {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.code-line {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    color: #00ffff;
    background: rgba(0, 255, 255, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

/* Research-specific product badge */
.research-hero .product-badge,
.product-hero .product-badge {
    background: linear-gradient(135deg, #1a1a2e, #0f3460);
    border: 1px solid rgba(0, 255, 255, 0.3);
}

/* Responsividade Research */
@media (max-width: 768px) {
    .research-hero {
        padding: 6rem 0 4rem;
    }

    .research-hero .hero-title {
        font-size: 2.5rem;
    }

    .research-hero .hero-stats {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1.5rem;
    }

    .research-hero .hero-cta {
        flex-direction: column;
        align-items: center;
    }

    .quantum-interface {
        padding: 1rem;
    }

    .code-line {
        font-size: 0.7rem;
    }
}

/* ===== ASIMOV IOT & SMARTCITY STYLES ===== */
.iot-hero {
    background: linear-gradient(135deg, #0f4c75 0%, #3282b8 50%, #bbe1fa 100%);
    color: white;
    padding: 8rem 0 6rem;
    position: relative;
    overflow: hidden;
}

.iot-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="iot-grid" width="15" height="15" patternUnits="userSpaceOnUse"><circle cx="7.5" cy="7.5" r="1.5" fill="rgba(255,255,255,0.2)"/><path d="M 0 7.5 L 15 7.5 M 7.5 0 L 7.5 15" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23iot-grid)"/></svg>');
    opacity: 0.3;
}

.iot-hero .container {
    position: relative;
    z-index: 2;
}

.iot-hero .hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.iot-hero .hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
}

.iot-hero .highlight {
    background: linear-gradient(135deg, #00ff88, #00d4ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.iot-hero .hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: 3rem;
    max-width: 600px;
    opacity: 0.9;
}

.iot-hero .hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.iot-hero .stat-item {
    text-align: center;
}

.iot-hero .stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #00ff88;
    margin-bottom: 0.5rem;
    text-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
}

.iot-hero .stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.iot-hero .hero-cta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* IoT Dashboard Preview */
.iot-dashboard {
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 12px;
}

.sensor-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.sensor-card {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.sensor-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #0f4c75, #3282b8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-bottom: 0.5rem;
}

.sensor-data {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.sensor-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #0f4c75;
    margin-bottom: 0.25rem;
}

.sensor-label {
    font-size: 0.8rem;
    color: #64748b;
    font-weight: 500;
}

.sensor-status {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.sensor-status.good {
    background: #10b981;
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.5);
}

.sensor-status.warning {
    background: #f59e0b;
    box-shadow: 0 0 8px rgba(245, 158, 11, 0.5);
}

.sensor-status.danger {
    background: #ef4444;
    box-shadow: 0 0 8px rgba(239, 68, 68, 0.5);
}

.map-preview {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    text-align: center;
}

.map-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: #64748b;
}

.map-placeholder i {
    font-size: 2rem;
    color: #0f4c75;
}

/* Status badge específico para IoT */
.status-badge.active {
    background: #dcfce7;
    color: #166534;
}

/* IoT-specific product badge */
.iot-hero .product-badge,
.product-hero .product-badge {
    background: linear-gradient(135deg, #0f4c75, #3282b8);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Responsividade IoT */
@media (max-width: 768px) {
    .iot-hero {
        padding: 6rem 0 4rem;
    }

    .iot-hero .hero-title {
        font-size: 2.5rem;
    }

    .iot-hero .hero-stats {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1.5rem;
    }

    .iot-hero .hero-cta {
        flex-direction: column;
        align-items: center;
    }

    .sensor-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .iot-dashboard {
        padding: 1rem;
    }

    .map-preview {
        padding: 1.5rem;
    }
}

/* ===== ASIMOV PRODUCTIVITY STYLES ===== */
.productivity-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    color: white;
    padding: 8rem 0 6rem;
    position: relative;
    overflow: hidden;
}

.productivity-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="productivity-grid" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="rgba(255,255,255,0.2)"/><path d="M 5 10 L 15 10 M 10 5 L 10 15" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23productivity-grid)"/></svg>');
    opacity: 0.3;
}

.productivity-hero .container {
    position: relative;
    z-index: 2;
}

.productivity-hero .hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.productivity-hero .hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
}

.productivity-hero .highlight {
    background: linear-gradient(135deg, #ff6b6b, #feca57);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.productivity-hero .hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: 3rem;
    max-width: 600px;
    opacity: 0.9;
}

.productivity-hero .hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.productivity-hero .stat-item {
    text-align: center;
}

.productivity-hero .stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #feca57;
    margin-bottom: 0.5rem;
    text-shadow: 0 0 20px rgba(254, 202, 87, 0.5);
}

.productivity-hero .stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.productivity-hero .hero-cta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Document Interface Preview */
.doc-interface {
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 12px;
}

.doc-toolbar {
    background: white;
    padding: 1rem;
    border-radius: 8px 8px 0 0;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 1rem;
}

.toolbar-section {
    display: flex;
    gap: 0.5rem;
}

.tool-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: white;
    color: #64748b;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tool-btn.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: transparent;
}

.tool-btn:hover {
    border-color: #667eea;
    color: #667eea;
}

.doc-content {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    margin-bottom: 1rem;
}

.doc-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e2e8f0;
}

.doc-text {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.text-line {
    padding: 0.25rem 0;
    color: #334155;
    font-size: 0.9rem;
}

.text-line.ai-generated {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    padding: 0.5rem;
    border-radius: 4px;
    border-left: 3px solid #667eea;
    color: #667eea;
    font-style: italic;
}

.ai-suggestions {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.suggestion-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #64748b;
    font-size: 0.8rem;
}

.suggestion-item i {
    color: #feca57;
}

/* Status badge específico para Productivity */
.status-badge.beta {
    background: #ddd6fe;
    color: #5b21b6;
}

/* Productivity-specific product badge */
.productivity-hero .product-badge,
.product-hero .product-badge {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Responsividade Productivity */
@media (max-width: 768px) {
    .productivity-hero {
        padding: 6rem 0 4rem;
    }

    .productivity-hero .hero-title {
        font-size: 2.5rem;
    }

    .productivity-hero .hero-stats {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1.5rem;
    }

    .productivity-hero .hero-cta {
        flex-direction: column;
        align-items: center;
    }

    .toolbar-section {
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    .tool-btn {
        font-size: 0.7rem;
        padding: 0.4rem 0.8rem;
    }

    .doc-interface {
        padding: 1rem;
    }

    .doc-content {
        padding: 1rem;
    }
}

/* ===== ASIMOV COMMERCE STYLES ===== */
.commerce-hero {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 50%, #ff9ff3 100%);
    color: white;
    padding: 8rem 0 6rem;
    position: relative;
    overflow: hidden;
}

.commerce-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="commerce-grid" width="25" height="25" patternUnits="userSpaceOnUse"><circle cx="12.5" cy="12.5" r="2" fill="rgba(255,255,255,0.2)"/><path d="M 8 12.5 L 17 12.5 M 12.5 8 L 12.5 17" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23commerce-grid)"/></svg>');
    opacity: 0.3;
}

.commerce-hero .container {
    position: relative;
    z-index: 2;
}

.commerce-hero .hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.commerce-hero .hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
}

.commerce-hero .highlight {
    background: linear-gradient(135deg, #feca57, #ff9ff3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.commerce-hero .hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: 3rem;
    max-width: 600px;
    opacity: 0.9;
}

.commerce-hero .hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.commerce-hero .stat-item {
    text-align: center;
}

.commerce-hero .stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #feca57;
    margin-bottom: 0.5rem;
    text-shadow: 0 0 20px rgba(254, 202, 87, 0.5);
}

.commerce-hero .stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.commerce-hero .hero-cta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Commerce Dashboard Preview */
.commerce-dashboard {
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 12px;
}

.dashboard-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.metric-card {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
}

.metric-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.metric-data {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.metric-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.metric-label {
    font-size: 0.8rem;
    color: #64748b;
    font-weight: 500;
}

.metric-trend {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.metric-trend.up {
    color: #10b981;
}

.metric-trend.down {
    color: #ef4444;
}

.chat-preview {
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.chat-header {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    font-size: 0.9rem;
}

.chat-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-left: auto;
}

.chat-status.online {
    background: #10b981;
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.5);
}

.chat-messages {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.message {
    padding: 0.5rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    max-width: 80%;
}

.message.bot {
    background: #f1f5f9;
    color: #334155;
    align-self: flex-start;
    border-bottom-left-radius: 4px;
}

.message.user {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    align-self: flex-end;
    border-bottom-right-radius: 4px;
}

/* Commerce-specific product badge */
.commerce-hero .product-badge,
.product-hero .product-badge {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Responsividade Commerce */
@media (max-width: 768px) {
    .commerce-hero {
        padding: 6rem 0 4rem;
    }

    .commerce-hero .hero-title {
        font-size: 2.5rem;
    }

    .commerce-hero .hero-stats {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1.5rem;
    }

    .commerce-hero .hero-cta {
        flex-direction: column;
        align-items: center;
    }

    .dashboard-metrics {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .metric-card {
        padding: 0.75rem;
    }

    .commerce-dashboard {
        padding: 1rem;
    }

    .chat-messages {
        padding: 0.75rem;
    }

    .message {
        font-size: 0.75rem;
        max-width: 90%;
    }
}

/* Dropdown Navigation Styles */
.nav-dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.dropdown-toggle .fa-chevron-down {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.nav-dropdown:hover .dropdown-toggle .fa-chevron-down {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 250px;
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--neutral-200);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
    padding: 0.5rem 0;
    margin-top: 0.5rem;
}

.nav-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--neutral-700);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
    border-radius: 0;
}

.dropdown-link:hover {
    background: var(--primary-light);
    color: var(--primary-color);
}

.dropdown-link.active {
    background: var(--primary-light);
    color: var(--primary-color);
    font-weight: 600;
}

.dropdown-link i {
    font-size: 1rem;
    width: 20px;
    text-align: center;
}

.dropdown-divider {
    height: 1px;
    background: var(--neutral-200);
    margin: 0.5rem 0;
}

/* Hide dropdown on mobile */
@media (max-width: 768px) {
    .nav-dropdown .dropdown-menu {
        display: none;
    }
}