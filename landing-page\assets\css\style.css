/* Landing Page CSS for ASIMOV TECH SOLUTIONS */

:root {
    /* Colors from main site */
    --primary-color: #2D7FF9; /* Azul mais vibrante e profissional */
    --primary-dark: #1C57B5; /* Azul escuro mais suave */
    --primary-light: #E6F0FF; /* Azul muito claro para fundos */
    --secondary-color: #F8FAFC; /* Cinza muito claro para fundos secundários */
    --accent-color: #00C2FF; /* Azul turquesa para destaques */

    /* Neutral colors */
    --neutral-50: #FFFFFF;
    --neutral-100: #F9FAFB;
    --neutral-200: #F1F5F9;
    --neutral-300: #E2E8F0;
    --neutral-400: #CBD5E1;
    --neutral-500: #94A3B8;
    --neutral-600: #64748B;
    --neutral-700: #475569;
    --neutral-800: #334155;
    --neutral-900: #1E293B;

    /* Text colors */
    --text-dark: var(--neutral-800);
    --text-muted: var(--neutral-600);
    --text-light: white;

    /* Background colors */
    --bg-color: var(--neutral-50);
    --bg-card: white;

    /* Specific colors */
    --light-blue: rgba(45, 127, 249, 0.1);
    --dark-blue: var(--primary-dark);

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    --gradient-dark: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    --gradient-light: linear-gradient(135deg, var(--neutral-50), var(--primary-light));

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;
    --space-32: 8rem;
    --space-40: 10rem;
    --space-48: 12rem;
    --space-56: 14rem;
    --space-64: 16rem;

    /* Font sizes */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;
    --font-size-7xl: 4.5rem;
    --font-size-8xl: 6rem;
    --font-size-9xl: 8rem;

    /* Line heights */
    --line-height-none: 1;
    --line-height-tight: 1.25;
    --line-height-snug: 1.375;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.625;
    --line-height-loose: 2;

    /* Border radius */
    --radius-sm: 0.125rem;
    --radius-md: 0.25rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    --shadow-none: none;
}

/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    color: var(--neutral-800);
    background-color: var(--bg-color);
    line-height: var(--line-height-normal);
    overflow-x: hidden;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    line-height: var(--line-height-tight);
    font-weight: 700;
    color: var(--neutral-900);
}

h1 {
    font-size: var(--font-size-4xl);
    letter-spacing: -0.025em;
}

h2 {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--space-6);
}

h3 {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--space-4);
}

p {
    margin-bottom: var(--space-4);
}

.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: inline;
}

/* Header */
.header {
    padding: var(--space-6) 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
}

.logo img {
    height: 50px;
    margin-right: var(--space-4);
}

.logo-text {
    display: flex;
    flex-direction: column;
}

.logo-primary {
    font-size: var(--font-size-xl);
    font-weight: 800;
    color: var(--primary-color);
    line-height: 1.1;
}

.logo-secondary {
    font-size: var(--font-size-xs);
    color: var(--neutral-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Hero Section */
.hero {
    padding: var(--space-20) 0;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, var(--neutral-50) 0%, var(--primary-light) 100%);
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%232D7FF9' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2V6h4V4H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E"),
        radial-gradient(circle at 20% 20%, rgba(45, 127, 249, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(0, 194, 255, 0.08) 0%, transparent 50%);
    z-index: 0;
}

.hero-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 550px;
    text-align: left;
    flex: 1;
}

.hero-image {
    position: relative;
    z-index: 1;
    flex: 0 0 40%;
    max-width: 450px;
    margin-left: auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    animation: float 6s ease-in-out infinite;
    /* Removemos border-radius e shadow para o mascote */
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-15px);
    }
    100% {
        transform: translateY(0px);
    }
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    background-color: var(--light-blue);
    color: var(--primary-color);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-full);
    margin-bottom: var(--space-6);
    font-weight: 500;
    font-size: var(--font-size-sm);
}

.hero-badge i {
    margin-right: var(--space-2);
}

.hero-title {
    font-size: var(--font-size-5xl);
    margin-bottom: var(--space-6);
    line-height: 1.2;
}

.hero-description {
    font-size: var(--font-size-xl);
    color: var(--neutral-700);
    margin-bottom: var(--space-8);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* CTA Button */
.cta-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-primary);
    color: white;
    font-weight: 600;
    font-size: var(--font-size-lg);
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-lg);
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    box-shadow: var(--shadow-md);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.cta-button i {
    margin-right: var(--space-3);
    font-size: 1.2em;
}

.cta-secondary {
    background: white;
    color: var(--primary-color);
    border: 1px solid var(--neutral-300);
    margin-left: var(--space-4);
}

.cta-secondary:hover {
    background: var(--neutral-100);
}

/* Clients Section */
.clients {
    padding: var(--space-12) 0;
    background-color: var(--neutral-100);
    text-align: center;
    overflow: hidden;
}

.clients-title {
    font-size: var(--font-size-lg);
    color: var(--neutral-600);
    margin-bottom: var(--space-8);
    font-weight: 600;
}

/* Carrossel de clientes */
.clients-carousel {
    position: relative;
    width: 100%;
    overflow: hidden;
    padding: var(--space-4) 0;
    margin: 0 auto;
}

.clients-track {
    display: flex;
    width: 200%; /* Para acomodar a duplicação */
    animation: scroll 60s linear infinite; /* Aumentamos o tempo para movimento mais lento */
}

.clients-slide {
    display: flex;
    align-items: center;
    width: 50%; /* Metade do track */
    padding: 0 var(--space-4);
}

.client-logo {
    height: 30px;
    width: auto;
    max-width: 70px;
    object-fit: contain;
    opacity: 0.7;
    filter: grayscale(100%);
    transition: all 0.3s ease;
    flex-shrink: 0;
    margin: 0 25px; /* Aumentamos o espaçamento entre logos */
}

@media (max-width: 768px) {
    .client-logo {
        height: 20px;
        max-width: 60px;
        margin: 0 10px;
    }

    .clients-track {
        animation: scroll 25s linear infinite;
    }
}

.client-logo:hover {
    opacity: 1;
    filter: grayscale(0%);
    transform: scale(1.05);
    animation-play-state: paused; /* Pausa a animação ao passar o mouse */
}

/* Ajustes específicos para logos problemáticos */
.client-logo[alt="Logo Cliente 1"] {
    max-width: 65px;
    margin-right: 30px; /* Espaçamento extra à direita do Google */
}

.clients-carousel:hover .clients-track {
    animation-play-state: paused; /* Pausa a animação ao passar o mouse no carrossel */
}

@keyframes scroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%); /* Move para a esquerda metade da largura */
    }
}

/* Features Section */
.features {
    padding: var(--space-16) 0;
}

.section-title {
    text-align: center;
    margin-bottom: var(--space-12);
}

.section-title h2 {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--space-4);
}

.section-title p {
    color: var(--neutral-600);
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-6);
    margin-top: var(--space-8);
}

.feature-card {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.feature-image {
    width: 100%;
    height: 180px;
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-4);
    overflow: hidden;
}

.feature-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.feature-card:hover .feature-image img {
    transform: scale(1.05);
}

/* Testimonials Section */
.testimonials {
    position: relative;
    padding: var(--space-16) 0;
    overflow: hidden;
}

.testimonials-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.testimonials-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(var(--neutral-200) 1px, transparent 1px),
                      linear-gradient(90deg, var(--neutral-200) 1px, transparent 1px);
    background-size: 30px 30px;
    opacity: 0.1;
}

.testimonials-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    height: 80%;
    background: radial-gradient(circle at center, var(--primary-light) 0%, transparent 70%);
    opacity: 0.3;
    filter: blur(100px);
}

.testimonials-container {
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    gap: var(--space-8);
    align-items: center;
}

.testimonials-showcase {
    position: relative;
    text-align: center;
}

.showcase-image {
    position: relative;
    display: inline-block;
}

.floating-image {
    max-width: 300px;
    height: auto;
    animation: float 6s ease-in-out infinite;
}

.showcase-metrics {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
    margin-top: var(--space-6);
}

.metric-badge {
    display: inline-flex;
    align-items: center;
    background: white;
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-full);
    box-shadow: var(--shadow-md);
    font-size: var(--font-size-sm);
    color: var(--neutral-700);
}

.metric-badge i {
    color: var(--primary-color);
    margin-right: var(--space-2);
}

.testimonial-card.premium {
    background: white;
    border-radius: var(--radius-2xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-lg);
    margin-bottom: var(--space-6);
    border: 1px solid var(--neutral-200);
    transition: all 0.3s ease;
}

.testimonial-card.premium:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-4);
}

.card-header i {
    font-size: var(--font-size-3xl);
    color: var(--primary-color);
    opacity: 0.2;
}

.company-badge {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    background: var(--primary-light);
    border-radius: var(--radius-full);
}

.company-badge img {
    height: 24px;
    width: auto;
}

.company-badge span {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--primary-color);
}

.testimonial-content p {
    font-size: var(--font-size-lg);
    line-height: 1.6;
    color: var(--neutral-800);
    margin-bottom: var(--space-6);
}

.testimonial-footer {
    border-top: 1px solid var(--neutral-200);
    padding-top: var(--space-4);
}

.author-info h4 {
    color: var(--neutral-900);
    font-size: var(--font-size-lg);
    margin-bottom: var(--space-1);
}

.author-info p {
    color: var(--neutral-600);
    font-size: var(--font-size-sm);
}

.success-metrics {
    display: flex;
    gap: var(--space-6);
    margin-top: var(--space-4);
}

.metric {
    text-align: center;
}

.metric-value {
    display: block;
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--space-1);
}

.metric-label {
    font-size: var(--font-size-sm);
    color: var(--neutral-600);
}

@media (max-width: 1024px) {
    .testimonials-container {
        grid-template-columns: 1fr;
    }

    .testimonials-showcase {
        margin-bottom: var(--space-8);
    }
}

@media (max-width: 768px) {
    .success-metrics {
        flex-direction: column;
        gap: var(--space-4);
    }

    .metric-badge {
        font-size: var(--font-size-xs);
    }

    .testimonial-content p {
        font-size: var(--font-size-base);
    }
}

/* Ajuste para a imagem de testimonials */
.testimonials-image img {
    max-width: 300px;
    height: auto;
    animation: float 6s ease-in-out infinite;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--light-blue);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-4);
}

.feature-icon i {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
}

/* Team Section */
.team-section {
    background: var(--neutral-100);
    padding: var(--space-4) 0;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-8);
}

.team-member {
    background: white;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    text-align: center;
    position: relative;
}

.team-member::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.team-member:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.team-member:hover::before {
    transform: scaleX(1);
}

.member-image {
    width: 100%;
    aspect-ratio: 1;
    overflow: hidden;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.team-member:hover .member-image img {
    transform: scale(1.05);
}

.member-info {
    padding: var(--space-6);
    background: white;
}

.member-info h3 {
    color: var(--text-dark);
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--space-2);
}

.member-role {
    display: inline-block;
    background: var(--primary-light);
    color: var(--primary-color);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
    transform: translateY(0);
    transition: transform 0.3s ease;
}

.member-social {
    margin-top: var(--space-4);
    display: flex;
    justify-content: center;
    gap: var(--space-3);
}

.member-social .social-link {
    color: var(--neutral-600);
    font-size: var(--font-size-xl);
    transition: var(--transition);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-full);
    background: var(--neutral-100);
}

.member-social .social-link:hover {
    color: var(--primary-color);
    background: var(--primary-light);
    transform: translateY(-2px);
}

/* Responsive */
@media (max-width: 1024px) {
    .team-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .team-grid {
        grid-template-columns: repeat(1, 1fr);
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
    }

    .member-info h3 {
        font-size: var(--font-size-lg);
    }
}


.feature-title {
    font-size: var(--font-size-xl);
    margin-bottom: var(--space-2);
}

.feature-description {
    color: var(--neutral-600);
    margin-bottom: var(--space-4);
}

.feature-list {
    list-style: none;
    margin-top: var(--space-4);
}

.feature-list li {
    display: flex;
    align-items: center;
    margin-bottom: var(--space-2);
}

.feature-list i {
    color: var(--primary-color);
    margin-right: var(--space-2);
}

/* Testimonials Section */
.testimonials {
    padding: var(--space-16) 0;
    background-color: var(--primary-light);
    position: relative;
}

.testimonials::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(circle at 20% 20%, rgba(45, 127, 249, 0.05) 0%, transparent 50%);
    z-index: 0;
}

.testimonials-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--space-8);
}

.testimonials-image {
    flex: 0 0 40%;
    max-width: 450px;
    position: relative;
    z-index: 1;
}

.testimonials-image img {
    width: 100%;
    height: auto;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
}

.testimonials-content {
    flex: 0 0 50%;
    max-width: 600px;
}

.testimonial-card {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    box-shadow: var(--shadow-md);
    position: relative;
    z-index: 1;
    margin-bottom: var(--space-6);
}

.testimonial-card:last-child {
    margin-bottom: 0;
}

.testimonial-content {
    font-size: var(--font-size-lg);
    font-style: italic;
    color: var(--neutral-700);
    margin-bottom: var(--space-4);
    position: relative;
}

.testimonial-content::before {
    content: '"';
    font-size: 4rem;
    color: var(--primary-light);
    position: absolute;
    top: -2rem;
    left: -1rem;
    font-family: Georgia, serif;
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.author-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: var(--space-3);
    object-fit: cover;
    border: 3px solid var(--primary-light);
}

.author-info h4 {
    font-size: var(--font-size-base);
    margin-bottom: 0;
}

.author-info p {
    font-size: var(--font-size-sm);
    color: var(--neutral-500);
    margin-bottom: 0;
}

/* Stats Section */
.stats {
    padding: var(--space-16) 0;
    background: linear-gradient(135deg, var(--neutral-100) 0%, var(--primary-light) 100%);
    position: relative;
    overflow: hidden;
}

.stats::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(45, 127, 249, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(0, 194, 255, 0.08) 0%, transparent 50%);
    z-index: 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: var(--space-8);
    position: relative;
    z-index: 1;
    margin-top: var(--space-8);
}

.stat-card {
    text-align: center;
    padding: var(--space-8);
    background-color: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
    opacity: 0.8;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stat-number {
    font-size: var(--font-size-5xl);
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    margin-bottom: var(--space-4);
    line-height: 1;
    position: relative;
    display: inline-block;
}

.stat-number::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
}

.stat-label {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--neutral-700);
    margin-top: var(--space-4);
}

/* CTA Section */
.cta-section {
    padding: var(--space-16) 0;
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    z-index: 0;
}

.cta-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 1;
}

.cta-content {
    flex: 0 0 55%;
    max-width: 600px;
}

.cta-image {
    flex: 0 0 40%;
    max-width: 400px;
}

.cta-image img {
    width: 100%;
    height: auto;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
}

.cta-title {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--space-6);
    color: white;
}

.cta-description {
    font-size: var(--font-size-lg);
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--space-8);
}

.cta-buttons {
    display: flex;
    gap: var(--space-4);
    flex-wrap: wrap;
}

.cta-section .cta-button {
    background: white;
    color: var(--primary-color);
}

.cta-section .cta-button:hover {
    background: var(--neutral-100);
}

/* Footer */
.footer {
    background-color: var(--neutral-900);
    color: var(--neutral-400);
    padding: var(--space-12) 0 var(--space-6);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--space-8);
    margin-bottom: var(--space-8);
}

.footer-logo {
    display: flex;
    align-items: center;
    margin-bottom: var(--space-4);
}

.footer-logo img {
    height: 40px;
    margin-right: var(--space-3);
}

.footer-logo h3 {
    color: white;
    font-size: var(--font-size-xl);
}

.footer-info {
    max-width: 300px;
}

.footer-info p {
    margin-bottom: var(--space-4);
    font-size: var(--font-size-sm);
}

.footer-links h4 {
    color: white;
    margin-bottom: var(--space-4);
}

.footer-links ul {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--space-2);
}

.footer-links a {
    color: var(--neutral-400);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: white;
}

.footer-social {
    display: flex;
    gap: var(--space-3);
    margin-top: var(--space-4);
}

.social-link {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background-color: var(--primary-color);
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: var(--space-6);
    text-align: center;
    font-size: var(--font-size-sm);
}

/* Floating WhatsApp Button */
.whatsapp-float {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background-color: #25D366;
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    text-align: center;
    font-size: 30px;
    box-shadow: var(--shadow-lg);
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
}

.whatsapp-float:hover {
    transform: scale(1.1);
}

.whatsapp-float i {
    margin-top: 2px;
}

/* Responsive styles */
@media (max-width: 768px) {
    .hero-container {
        flex-direction: column;
    }

    .hero-content {
        text-align: center;
        margin-bottom: var(--space-8);
    }

    .hero-image {
        margin: 0 auto;
    }

    .hero-title {
        font-size: var(--font-size-4xl);
    }

    .hero-description {
        font-size: var(--font-size-lg);
    }

    .cta-buttons {
        flex-direction: column;
        gap: var(--space-4);
    }

    .cta-button {
        width: 100%;
    }

    .cta-secondary {
        margin-left: 0;
        margin-top: var(--space-4);
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        flex-direction: column;
        gap: var(--space-8);
    }
}

@media (max-width: 480px) {
    .hero {
        padding: var(--space-12) 0;
    }

    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .hero-description {
        font-size: var(--font-size-base);
    }

    .section-title h2 {
        font-size: var(--font-size-2xl);
    }
}

/* Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.8s ease forwards;
}

.delay-1 {
    animation-delay: 0.2s;
}

.delay-2 {
    animation-delay: 0.4s;
}

.delay-3 {
    animation-delay: 0.6s;
}

.delay-4 {
    animation-delay: 0.8s;
}

/* Pulse animation for CTA button */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(45, 127, 249, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(45, 127, 249, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(45, 127, 249, 0);
    }
}

.pulse {
    animation: pulse 2s infinite;
}
