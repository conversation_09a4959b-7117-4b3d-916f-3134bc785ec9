/* Seção de Equipe */
.team-section {
    padding: var(--space-20) 0;
    background: linear-gradient(135deg, var(--neutral-50) 0%, var(--primary-light) 100%);
    position: relative;
    overflow: hidden;
}

.team-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(45, 127, 249, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(0, 194, 255, 0.08) 0%, transparent 50%);
    z-index: 0;
}

.team-section .container {
    position: relative;
    z-index: 1;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-12);
    padding: 0 var(--space-4);
}

.team-member {
    background: white;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    text-align: center;
    position: relative;
}

.team-member::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.team-member:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.team-member:hover::before {
    transform: scaleX(1);
}

.member-image {
    width: 100%;
    aspect-ratio: 1;
    overflow: hidden;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.team-member:hover .member-image img {
    transform: scale(1.05);
}

.member-info {
    padding: var(--space-6);
    background: white;
}

.member-info h3 {
    color: var(--text-dark);
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--space-2);
}

.member-role {
    display: inline-block;
    background: var(--primary-light);
    color: var(--primary-color);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
    transform: translateY(0);
    transition: transform 0.3s ease;
}

.team-member:hover .member-role {
    transform: translateY(-2px);
}

.member-social {
    margin-top: var(--space-4);
    display: flex;
    justify-content: center;
    gap: var(--space-3);
}

.member-social .social-link {
    color: var(--neutral-600);
    font-size: var(--font-size-xl);
    transition: var(--transition);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-full);
    background: var(--neutral-100);
}

.member-social .social-link:hover {
    color: var(--primary-color);
    background: var(--primary-light);
    transform: translateY(-2px);
}

/* Responsividade */
@media (max-width: 1024px) {
    .team-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .team-grid {
        grid-template-columns: repeat(1, 1fr);
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .member-info h3 {
        font-size: var(--font-size-lg);
    }
}