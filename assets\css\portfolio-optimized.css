/* Estilos otimizados para a página de portfólio */
:root {
    --card-radius: 16px;
    --card-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
    --card-hover-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    --transition-speed: 0.3s;
    --spacing-unit: 1.5rem;

    /* Cores refinadas */
    --bg-light: #f8f9fc;
    --bg-card: #ffffff;
    --text-primary: #1a202c;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --accent-primary: #3182ce;
    --accent-secondary: #4299e1;
    --border-light: #e2e8f0;
    --tag-bg: #ebf8ff;
    --tag-text: #2b6cb0;
    --tech-bg: #f7fafc;
    --tech-text: #4a5568;
    --tech-icon: #3182ce;
}

/* Layout da página */
body {
    background-color: var(--bg-light);
}

/* Cabeçalho */
.portfolio-header {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    padding: 120px 0 70px;
    text-align: center;
    color: #fff;
    position: relative;
    overflow: hidden;
}

.portfolio-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(0,102,255,0.15) 0%, transparent 70%);
    pointer-events: none;
}

.header-content {
    max-width: 900px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.main-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    color: #fff;
    position: relative;
    display: inline-block;
    letter-spacing: -0.5px;
    line-height: 1.2;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.main-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: #0066ff;
    border-radius: 2px;
}

.main-title .highlight {
    color: #0066ff;
}

.subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin: 1.5rem auto 0;
    max-width: 800px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

/* Filtros */
.portfolio-filters {
    background: var(--bg-card);
    padding: 1rem 0 2rem;
    margin-bottom: 2rem;
    border-bottom: 1px solid var(--border-light);
}

.filter-buttons {
    display: flex;
    justify-content: center;
    gap: 0.75rem;
    flex-wrap: wrap;
    padding: 0 1rem;
}

.filter-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    background: var(--tech-bg);
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    font-size: 0.95rem;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--accent-primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(49, 130, 206, 0.2);
}

/* Lista de projetos */
.projects-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2.5rem;
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-unit) 1rem;
}

.project-item {
    display: flex;
    flex-direction: column;
    background: var(--bg-card);
    border-radius: var(--card-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    transition: transform var(--transition-speed) ease, box-shadow var(--transition-speed) ease;
    border: 1px solid var(--border-light);
    height: 100%;
    position: relative;
    border-top: 4px solid var(--accent-primary);
}

.project-item:hover {
    transform: translateY(-8px);
    box-shadow: var(--card-hover-shadow);
}

/* Conteúdo do projeto */
.project-content {
    padding: 2rem;
    display: flex;
    flex-direction: column;
    flex: 1;
}

.project-title {
    font-size: 1.6rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
    position: relative;
    padding-bottom: 0.75rem;
}

.project-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: var(--accent-primary);
    border-radius: 3px;
}

.project-description {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.7;
    font-size: 1rem;
}

/* Tags e tecnologias */
.project-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.25rem;
}

.project-category {
    background: var(--tag-bg);
    color: var(--tag-text);
    padding: 0.35rem 0.75rem;
    border-radius: 30px;
    font-size: 0.85rem;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(43, 108, 176, 0.1);
    transition: all 0.2s ease;
}

.project-tech-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    list-style: none;
    padding: 0;
}

.tech-item {
    background: var(--tech-bg);
    color: var(--tech-text);
    padding: 0.35rem 0.75rem;
    border-radius: 30px;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 0.35rem;
    transition: all 0.2s ease;
    border: 1px solid var(--border-light);
}

.tech-item i {
    color: var(--tech-icon);
    font-size: 0.9rem;
}

/* Rodapé do projeto */
.project-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: auto;
    padding-top: 1rem;
    border-top: 1px solid var(--border-light);
}

.project-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-muted);
}

.project-status i.fa-check-circle {
    color: #48bb78;
    font-size: 1.1rem;
}

.project-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.6rem 1.2rem;
    border-radius: 30px;
    background-color: var(--accent-primary);
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
    text-decoration: none;
    transition: all var(--transition-speed) ease;
}

.project-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(49, 130, 206, 0.3);
    background: linear-gradient(to right, var(--accent-primary), var(--accent-secondary));
}

.project-link.disabled {
    background-color: var(--tech-bg);
    color: var(--text-muted);
    cursor: not-allowed;
    border: 1px solid var(--border-light);
}

.project-link.disabled:hover {
    background-color: var(--tech-bg);
    transform: none;
    box-shadow: none;
}

/* Responsividade */
@media (max-width: 1200px) {
    .projects-list {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: 2rem;
    }
}

@media (max-width: 768px) {
    .projects-list {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1.5rem;
    }
    
    .project-content {
        padding: 1.5rem;
    }
    
    .main-title {
        font-size: 2.5rem;
    }
}

@media (max-width: 576px) {
    .projects-list {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .project-title {
        font-size: 1.4rem;
    }
    
    .project-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .project-link {
        width: 100%;
        justify-content: center;
    }
}
