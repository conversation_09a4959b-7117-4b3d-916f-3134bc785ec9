/* Estilos modernos para a página de portfólio */
:root {
    --card-border-radius: 16px;
    --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    --card-hover-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    --transition-speed: 0.3s;
    --spacing-unit: 1.5rem;
}

/* Introdução do portfólio */
.portfolio-intro {
    padding: calc(100px + var(--space-12)) 0 var(--space-8);
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.portfolio-intro h1 {
    font-size: clamp(2rem, 5vw, 3rem);
    margin-bottom: var(--space-4);
    color: var(--neutral-900);
}

.portfolio-intro p {
    font-size: var(--font-size-lg);
    color: var(--neutral-600);
    line-height: 1.6;
    margin-bottom: var(--space-8);
}

/* Filtros */
.portfolio-filters {
    background: white;
    padding: var(--space-4) 0 var(--space-8);
    border-bottom: 1px solid var(--neutral-200);
    margin-bottom: var(--space-8);
}

.filter-buttons {
    display: flex;
    justify-content: center;
    gap: var(--space-3);
    flex-wrap: wrap;
    padding: 0 var(--space-4);
}

.filter-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid var(--neutral-200);
    border-radius: 50px;
    background: white;
    color: var(--neutral-700);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    position: relative;
    z-index: 1;
    font-size: 0.95rem;
}

.filter-btn::after {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--gradient-primary);
    border-radius: 50px;
    opacity: 0;
    z-index: -1;
    transition: opacity var(--transition-speed) ease;
}

.filter-btn:hover,
.filter-btn.active {
    color: white;
    border-color: transparent;
}

.filter-btn:hover::after,
.filter-btn.active::after {
    opacity: 1;
}

.filter-btn:not(.active):hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Lista de projetos */
.projects-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-unit);
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-unit) var(--space-4);
}

.project-item {
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: var(--card-border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    transition: all var(--transition-speed) ease;
    border: 1px solid var(--neutral-200);
}

.project-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-hover-shadow);
    border-color: var(--primary-light);
}

@media (min-width: 768px) {
    .project-item {
        flex-direction: row;
        align-items: stretch;
    }
}

/* Imagem do projeto */
.project-image-container {
    position: relative;
    overflow: hidden;
    background-color: var(--neutral-100);
    display: flex;
    flex-direction: column;
}

@media (max-width: 767px) {
    .project-image-container {
        height: auto;
    }
}

@media (min-width: 768px) {
    .project-image-container {
        flex: 0 0 40%;
        max-width: 40%;
    }
}

.project-main-image {
    position: relative;
    overflow: hidden;
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--neutral-900);
    min-height: 300px;
    padding: 1rem;
}

.project-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: transform 0.5s ease;
}

.project-item:hover .project-image {
    transform: scale(1.05);
}

/* Galeria de miniaturas */
.project-thumbnails {
    display: flex;
    padding: 0.75rem;
    gap: 0.75rem;
    background-color: var(--neutral-800);
    justify-content: center;
    flex-wrap: wrap;
}

.thumbnail-item {
    width: 70px;
    height: 70px;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    opacity: 0.7;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    position: relative;
}

.thumbnail-item::after {
    content: '';
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.2);
    transition: background 0.3s ease;
}

.thumbnail-item:hover {
    opacity: 1;
    border-color: var(--primary-light);
    transform: translateY(-3px);
}

.thumbnail-item:hover::after {
    background: rgba(0, 0, 0, 0);
}

.thumbnail-item.active {
    opacity: 1;
    border-color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.thumbnail-item.active::after {
    background: rgba(0, 0, 0, 0);
}

.thumbnail-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

@media (max-width: 767px) {
    .project-main-image {
        height: 250px;
        min-height: 250px;
    }

    .thumbnail-item {
        width: 60px;
        height: 60px;
    }

    .project-image-container {
        margin-bottom: 0;
    }

    .project-thumbnails {
        padding: 0.5rem;
        gap: 0.5rem;
    }
}

/* Conteúdo do projeto */
.project-content {
    padding: var(--spacing-unit);
    display: flex;
    flex-direction: column;
    flex: 1;
}

@media (min-width: 768px) {
    .project-content {
        padding: var(--spacing-unit) var(--spacing-unit) var(--spacing-unit) calc(var(--spacing-unit) * 1.5);
    }
}

.project-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: var(--neutral-900);
}

.project-description {
    color: var(--neutral-600);
    margin-bottom: 1.25rem;
    line-height: 1.6;
    flex-grow: 1;
}

/* Tags e tecnologias */
.project-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.25rem;
}

.project-category {
    background: var(--primary-light);
    color: var(--primary-color);
    padding: 0.35rem 0.75rem;
    border-radius: 50px;
    font-size: 0.85rem;
    font-weight: 500;
}

.project-tech-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    list-style: none;
    padding: 0;
}

.tech-item {
    background: var(--neutral-100);
    color: var(--neutral-700);
    padding: 0.35rem 0.75rem;
    border-radius: 50px;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 0.35rem;
}

.tech-item i {
    color: var(--primary-color);
    font-size: 0.9rem;
}

/* Rodapé do projeto */
.project-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: auto;
    padding-top: 1rem;
    border-top: 1px solid var(--neutral-200);
}

.project-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--neutral-600);
}

.project-status i.fa-check-circle {
    color: #4CAF50;
    font-size: 1.1rem;
}

.project-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    border-radius: 50px;
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
    text-decoration: none;
    transition: all var(--transition-speed) ease;
}

.project-link:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.project-link.disabled {
    background-color: var(--neutral-200);
    color: var(--neutral-600);
    cursor: not-allowed;
}

.project-link.disabled:hover {
    background-color: var(--neutral-200);
    transform: none;
    box-shadow: none;
}

/* Responsividade */
@media (max-width: 576px) {
    .project-title {
        font-size: 1.5rem;
    }

    .project-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .project-link {
        width: 100%;
        justify-content: center;
    }
}
