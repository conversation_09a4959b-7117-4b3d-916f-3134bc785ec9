/* Estilos otimizados para a página de portfólio - SEM ANIMAÇÕES */
:root {
    --card-radius: 12px;
    --card-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    --spacing-unit: 1.5rem;

    /* Cores refinadas */
    --bg-light: #f8f9fc;
    --bg-card: #ffffff;
    --text-primary: #1a202c;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --accent-primary: #3182ce;
    --border-light: #e2e8f0;
    --tag-bg: #ebf8ff;
    --tag-text: #2b6cb0;
    --tech-bg: #f7fafc;
    --tech-text: #4a5568;
    --tech-icon: #3182ce;
}

/* Layout da página */
body {
    background-color: var(--bg-light);
}

/* Cabeçalho */
.portfolio-header {
    background: #1a1a2e;
    padding: 80px 0 50px;
    text-align: center;
    color: #fff;
}

.header-content {
    max-width: 900px;
    margin: 0 auto;
}

.main-title {
    font-size: 2.8rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    color: #fff;
    line-height: 1.2;
}

.main-title .highlight {
    color: #0066ff;
}

.subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin: 1.5rem auto 0;
    max-width: 800px;
}

/* Filtros */
.portfolio-filters {
    background: var(--bg-card);
    padding: 1rem 0 1.5rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-light);
}

.filter-buttons {
    display: flex;
    justify-content: center;
    gap: 0.75rem;
    flex-wrap: wrap;
    padding: 0 1rem;
}

.filter-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    background: var(--tech-bg);
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    font-size: 0.95rem;
}

.filter-btn.active {
    background: var(--accent-primary);
    color: white;
}

/* Lista de projetos */
.projects-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-unit) 1rem;
}

.project-item {
    display: flex;
    flex-direction: column;
    background: var(--bg-card);
    border-radius: var(--card-radius);
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-light);
    height: 100%;
    border-top: 3px solid var(--accent-primary);
}

/* Conteúdo do projeto */
.project-content {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    flex: 1;
}

.project-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
    position: relative;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid var(--accent-primary);
}

.project-description {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
    font-size: 1rem;
}

/* Tags e tecnologias */
.project-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.25rem;
}

.project-category {
    background: var(--tag-bg);
    color: var(--tag-text);
    padding: 0.35rem 0.75rem;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
}

.project-tech-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    list-style: none;
    padding: 0;
}

.tech-item {
    background: var(--tech-bg);
    color: var(--tech-text);
    padding: 0.35rem 0.75rem;
    border-radius: 4px;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 0.35rem;
    border: 1px solid var(--border-light);
}

.tech-item i {
    color: var(--tech-icon);
    font-size: 0.9rem;
}

/* Rodapé do projeto */
.project-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: auto;
    padding-top: 1rem;
    border-top: 1px solid var(--border-light);
}

.project-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-muted);
}

.project-status i.fa-check-circle {
    color: #48bb78;
    font-size: 1.1rem;
}

.project-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.6rem 1.2rem;
    border-radius: 4px;
    background-color: var(--accent-primary);
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
    text-decoration: none;
}

.project-link.disabled {
    background-color: var(--tech-bg);
    color: var(--text-muted);
    cursor: not-allowed;
    border: 1px solid var(--border-light);
}

/* Responsividade */
@media (max-width: 1200px) {
    .projects-list {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .projects-list {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1.5rem;
    }

    .project-content {
        padding: 1.25rem;
    }

    .main-title {
        font-size: 2.2rem;
    }
}

@media (max-width: 576px) {
    .projects-list {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .project-title {
        font-size: 1.4rem;
    }

    .project-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .project-link {
        width: 100%;
        justify-content: center;
    }
}

/* ===== CORREÇÃO DE ALINHAMENTO DOS TÍTULOS ===== */
.portfolio-header .main-title,
.products-header .section-title,
.custom-projects-header .section-title {
    position: static !important;
    left: auto !important;
    transform: none !important;
    text-align: center;
    display: block;
    margin-left: auto;
    margin-right: auto;
}

/* ===== SEÇÃO NOSSOS PRODUTOS ===== */
.our-products {
    background: linear-gradient(135deg, #f8f9fc 0%, #e9ecef 100%);
    padding: 4rem 0;
    margin-top: 2rem;
}

.products-header {
    text-align: center;
    margin-bottom: 3rem;
}

.products-header .section-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.products-header .section-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Vertical de produtos */
.product-vertical {
    background: var(--bg-card);
    border-radius: 16px;
    margin-bottom: 2.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--border-light);
    overflow: hidden;
}

.vertical-header {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.vertical-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: #0066ff;
}

.vertical-info h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.5rem;
}

.vertical-description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
    margin: 0;
}

/* Grid de produtos */
.products-grid {
    padding: 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

/* Card de produto */
.product-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--border-light);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.product-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    gap: 1rem;
}

.product-name {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    flex: 1;
    line-height: 1.3;
}

.product-status {
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    white-space: nowrap;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.product-status:not(.featured) {
    background: var(--tag-bg);
    color: var(--tag-text);
}

.product-status.featured {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.product-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

/* Tags de tecnologia */
.product-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tech-tag {
    background: var(--tech-bg);
    color: var(--tech-text);
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid var(--border-light);
}

/* CTA dos produtos */
.products-cta {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border-radius: 16px;
    padding: 3rem 2rem;
    text-align: center;
    margin-top: 2rem;
}

.products-cta .cta-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1rem;
}

.products-cta .cta-content p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
    margin-bottom: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.products-cta .button {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #0066ff, #0052cc);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.products-cta .button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 102, 255, 0.3);
}

/* Responsividade para produtos */
@media (max-width: 1200px) {
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    }
}

@media (max-width: 768px) {
    .our-products {
        padding: 3rem 0;
    }

    .products-header .section-title {
        font-size: 2rem;
    }

    .vertical-header {
        padding: 1.5rem;
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .vertical-info h3 {
        font-size: 1.5rem;
    }

    .products-grid {
        grid-template-columns: 1fr;
        padding: 1.5rem;
        gap: 1.5rem;
    }

    .product-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .products-cta {
        padding: 2rem 1.5rem;
    }

    .products-cta .cta-content h3 {
        font-size: 1.6rem;
    }
}

@media (max-width: 576px) {
    .products-header .section-title {
        font-size: 1.8rem;
    }

    .vertical-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .product-card {
        padding: 1.25rem;
    }

    .product-name {
        font-size: 1.2rem;
    }
}

/* ===== SEÇÃO PROJETOS CUSTOMIZADOS ===== */
.custom-projects-header {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    padding: 3rem 0 2rem;
    margin-top: 2rem;
    text-align: center;
    color: #fff;
}

.custom-projects-header .section-title {
    font-size: 2.2rem;
    font-weight: 800;
    color: white;
    margin-bottom: 1rem;
}

.custom-projects-header .section-subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

@media (max-width: 768px) {
    .custom-projects-header {
        padding: 2rem 0 1.5rem;
    }

    .custom-projects-header .section-title {
        font-size: 1.8rem;
    }

    .custom-projects-header .section-subtitle {
        font-size: 1rem;
        padding: 0 1rem;
    }
}
