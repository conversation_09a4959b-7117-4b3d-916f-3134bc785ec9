/* ===== ASIMOV FINTECH STYLES - CRYPTO THEME ===== */

/* Override ASIMOV logo colors to use blue theme */
.navbar .logo-primary,
.footer .logo-primary {
    color: var(--asimov-blue) !important;
}

:root {
    /* Fintech Color Palette - Gold & Black Theme */
    --fintech-primary: #FFD700;      /* Gold */
    --fintech-secondary: #FFA500;    /* Orange Gold */
    --fintech-accent: #FFED4E;       /* Light Gold */
    --fintech-dark: #0A0A0A;         /* Deep Black */
    --fintech-dark-alt: #1A1A1A;     /* Dark Gray */
    --fintech-dark-light: #2A2A2A;   /* Medium Gray */
    --fintech-text-light: #F5F5F5;   /* Light Text */
    --fintech-text-muted: #CCCCCC;   /* Muted Text */
    --fintech-success: #00FF88;      /* Crypto Green */
    --fintech-danger: #FF4757;       /* Crypto Red */

    /* ASIMOV Brand Colors - Blue Theme */
    --asimov-blue: #2D7FF9;          /* Primary Blue for ASIMOV logo */
    --asimov-blue-dark: #1C57B5;     /* Darker Blue */

    /* Gradients */
    --fintech-gradient-primary: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    --fintech-gradient-dark: linear-gradient(135deg, #0A0A0A 0%, #1A1A1A 100%);
    --fintech-gradient-accent: linear-gradient(135deg, #FFED4E 0%, #FFD700 100%);
}

/* Hero Section */
.fintech-hero {
    background: var(--fintech-gradient-dark);
    padding: 140px 0 100px;
    color: var(--fintech-text-light);
    position: relative;
    overflow: hidden;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.fintech-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 165, 0, 0.08) 0%, transparent 50%),
        url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23FFD700' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2V6h4V4H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    z-index: 0;
}

.fintech-hero .hero-content {
    position: relative;
    z-index: 1;
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
}

.fintech-hero .hero-badge {
    background: rgba(255, 215, 0, 0.15);
    color: var(--fintech-primary);
    padding: 1rem 2rem;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 700;
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 2.5rem;
    border: 2px solid rgba(255, 215, 0, 0.3);
    backdrop-filter: blur(10px);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.fintech-hero .hero-badge i {
    font-size: 1.2rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.fintech-hero .hero-title {
    font-size: 4.5rem;
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 2rem;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.fintech-hero .gradient-text {
    background: var(--fintech-gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.fintech-hero .gradient-text::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: var(--fintech-gradient-primary);
    border-radius: 2px;
}

.fintech-hero .hero-description {
    font-size: 1.4rem;
    line-height: 1.7;
    margin-bottom: 4rem;
    color: var(--fintech-text-muted);
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

/* Hero Stats */
.hero-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 3rem;
    margin-bottom: 4rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.stat-item {
    text-align: center;
    background: rgba(255, 215, 0, 0.1);
    padding: 2rem 1.5rem;
    border-radius: 20px;
    border: 1px solid rgba(255, 215, 0, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 215, 0, 0.15);
    border-color: rgba(255, 215, 0, 0.4);
}

.stat-number {
    font-size: 3rem;
    font-weight: 900;
    color: var(--fintech-primary);
    line-height: 1;
    margin-bottom: 0.75rem;
    text-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
}

.stat-label {
    font-size: 1rem;
    color: var(--fintech-text-muted);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
}

/* Hero CTA */
.fintech-hero .hero-cta {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.fintech-hero .button {
    padding: 1.25rem 2.5rem;
    border-radius: 50px;
    font-weight: 700;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.4s ease;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.fintech-hero .button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.fintech-hero .button:hover::before {
    left: 100%;
}

.fintech-hero .button-primary {
    background: var(--fintech-gradient-primary);
    color: var(--fintech-dark);
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
}

.fintech-hero .button-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(255, 215, 0, 0.4);
}

.fintech-hero .button-secondary {
    background: transparent;
    color: var(--fintech-primary);
    border: 2px solid var(--fintech-primary);
}

.fintech-hero .button-secondary:hover {
    background: var(--fintech-primary);
    color: var(--fintech-dark);
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
}

/* Produto Herói */
.hero-product {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--fintech-dark) 0%, var(--fintech-dark-alt) 100%);
    position: relative;
    overflow: hidden;
}

.hero-product::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 30% 30%, rgba(255, 215, 0, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(255, 165, 0, 0.03) 0%, transparent 50%);
    z-index: 0;
}

.product-hero-header {
    text-align: center;
    margin-bottom: 5rem;
    position: relative;
    z-index: 1;
}

.hero-badge.featured {
    background: var(--fintech-gradient-primary);
    color: var(--fintech-dark);
    padding: 0.75rem 1.5rem;
    border-radius: 30px;
    font-size: 1rem;
    font-weight: 700;
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
}

.hero-badge.featured i {
    animation: rotate 2s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.product-hero-header .section-title {
    font-size: 4rem;
    font-weight: 900;
    color: var(--fintech-text-light);
    margin-bottom: 1.5rem;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    text-align: center;
}

.product-hero-header .section-subtitle {
    font-size: 1.3rem;
    color: var(--fintech-text-muted);
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.6;
    text-align: center;
}

.fintech-products .section-title {
    font-size: 3rem;
    font-weight: 800;
    color: var(--fintech-dark);
    margin-bottom: 1rem;
    text-align: center;
}

.fintech-products .section-subtitle {
    font-size: 1.2rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

/* Layout reorganizado - Features em cima, Pricing embaixo */
.hero-features {
    margin-bottom: 5rem;
    position: relative;
    z-index: 1;
}

.hero-pricing {
    position: relative;
    z-index: 1;
}

/* Features Grid */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2.5rem;
}

.feature-item {
    background: var(--fintech-dark-light);
    padding: 2.5rem;
    border-radius: 20px;
    border: 1px solid rgba(255, 215, 0, 0.1);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--fintech-gradient-primary);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
}

.feature-item:hover::before {
    transform: scaleX(1);
}

.feature-item:hover {
    transform: translateY(-10px);
    border-color: rgba(255, 215, 0, 0.3);
    background: rgba(42, 42, 42, 0.8);
    box-shadow: 0 20px 40px rgba(255, 215, 0, 0.1);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--fintech-gradient-primary);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    color: var(--fintech-dark);
    font-size: 1.5rem;
    box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3);
}

.feature-item h4 {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--fintech-text-light);
    margin-bottom: 1rem;
}

.feature-item p {
    color: var(--fintech-text-muted);
    line-height: 1.7;
    font-size: 1.1rem;
}

/* Pricing */
.hero-pricing {
    background: var(--fintech-dark-light);
    padding: 4rem;
    border-radius: 25px;
    border: 1px solid rgba(255, 215, 0, 0.2);
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 1200px;
    margin: 0 auto;
}

.hero-pricing::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(255, 215, 0, 0.05) 0%, transparent 70%);
    z-index: 0;
}

.hero-pricing > * {
    position: relative;
    z-index: 1;
}

.hero-pricing h3 {
    font-size: 2rem;
    font-weight: 800;
    color: var(--fintech-text-light);
    margin-bottom: 2.5rem;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
}

.pricing-card {
    padding: 2.5rem;
    border: 2px solid rgba(255, 215, 0, 0.2);
    border-radius: 20px;
    transition: all 0.4s ease;
    background: rgba(26, 26, 26, 0.8);
    backdrop-filter: blur(10px);
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.pricing-card:hover {
    transform: translateY(-10px);
    border-color: rgba(255, 215, 0, 0.6);
    background: rgba(26, 26, 26, 0.9);
    box-shadow: 0 20px 40px rgba(255, 215, 0, 0.2);
}

.pricing-card.featured {
    border-color: var(--fintech-primary);
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.15) 0%, rgba(255, 165, 0, 0.1) 100%);
    transform: scale(1.05);
    position: relative;
    box-shadow: 0 25px 50px rgba(255, 215, 0, 0.3);
}

.pricing-card.featured::before {
    content: 'MAIS POPULAR';
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--fintech-gradient-primary);
    color: var(--fintech-dark);
    padding: 0.75rem 1.5rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 700;
    letter-spacing: 1px;
    text-transform: uppercase;
    box-shadow: 0 10px 25px rgba(255, 215, 0, 0.4);
}

.pricing-card h4 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--fintech-text-light);
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.price {
    font-size: 2.5rem;
    font-weight: 900;
    color: var(--fintech-primary);
    margin-bottom: 2rem;
    text-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
}

.price span {
    font-size: 1.2rem;
    color: var(--fintech-text-muted);
    font-weight: 600;
}

.features-list {
    list-style: none;
    padding: 0;
    margin-bottom: 2rem;
    flex-grow: 1;
}

.features-list li {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.2rem;
    color: var(--fintech-text-muted);
    font-size: 1.1rem;
    font-weight: 500;
    position: relative;
    padding-left: 0.5rem;
}

.features-list li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 3px;
    background: var(--fintech-primary);
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.features-list i {
    color: var(--fintech-primary);
    font-size: 1.1rem;
    width: 24px;
    height: 24px;
    background: rgba(255, 215, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(255, 215, 0, 0.3);
    flex-shrink: 0;
}

/* Pricing CTAs */
.pricing-cta {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    margin-top: 1.5rem;
    background: transparent;
    color: var(--fintech-primary);
    border: 2px solid var(--fintech-primary);
    border-radius: 25px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.pricing-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--fintech-gradient-primary);
    transition: 0.4s;
    z-index: 0;
}

.pricing-cta:hover::before {
    left: 0;
}

.pricing-cta:hover {
    color: var(--fintech-dark);
    border-color: var(--fintech-primary);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3);
}

.pricing-cta i,
.pricing-cta span {
    position: relative;
    z-index: 1;
}

.pricing-cta.featured {
    background: var(--fintech-gradient-primary);
    color: var(--fintech-dark);
    border-color: var(--fintech-primary);
    box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3);
}

.pricing-cta.featured::before {
    background: var(--fintech-gradient-accent);
}

.pricing-cta.featured:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(255, 215, 0, 0.4);
}

/* Produtos Grid */
.fintech-products {
    padding: 100px 0;
    background: linear-gradient(135deg, #F5F5F5 0%, #E8E8E8 100%);
}

.fintech-products .section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.fintech-products .section-title {
    font-size: 3rem;
    font-weight: 800;
    color: var(--fintech-dark);
    margin-bottom: 1rem;
}

.fintech-products .section-subtitle {
    font-size: 1.2rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
    margin-top: 4rem;
}

.product-card {
    background: white;
    border-radius: 25px;
    padding: 2.5rem;
    border: 1px solid rgba(255, 215, 0, 0.2);
    transition: all 0.4s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--fintech-gradient-primary);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
}

.product-card:hover::before {
    transform: scaleX(1);
}

.product-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 25px 50px rgba(255, 215, 0, 0.2);
    border-color: var(--fintech-primary);
}

.product-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.product-icon {
    width: 60px;
    height: 60px;
    background: var(--fintech-gradient-primary);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--fintech-dark);
    font-size: 1.5rem;
    box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3);
}

.product-status {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 700;
    background: var(--fintech-gradient-primary);
    color: var(--fintech-dark);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.product-card h3 {
    font-size: 1.8rem;
    font-weight: 800;
    color: var(--fintech-dark);
    margin-bottom: 1.5rem;
}

.product-card p {
    color: #666;
    line-height: 1.7;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.product-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 2rem;
}

.product-tech span {
    background: rgba(255, 215, 0, 0.1);
    color: var(--fintech-dark);
    padding: 0.5rem 1rem;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 600;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.product-link {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--fintech-dark);
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.product-link:hover {
    gap: 1rem;
    color: var(--fintech-secondary);
}

.product-link i {
    transition: transform 0.3s ease;
    background: var(--fintech-gradient-primary);
    color: var(--fintech-dark);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}

.product-link:hover i {
    transform: translateX(5px);
}

/* CTA Section */
.cta-section {
    background: var(--fintech-gradient-dark);
    padding: 120px 0;
    text-align: center;
    color: var(--fintech-text-light);
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 165, 0, 0.08) 0%, transparent 50%);
    z-index: 0;
}

.cta-content {
    position: relative;
    z-index: 1;
}

.cta-content h2 {
    font-size: 3.5rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
    background: var(--fintech-gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.cta-content p {
    font-size: 1.4rem;
    color: var(--fintech-text-muted);
    margin-bottom: 3rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.cta-section .button {
    padding: 1.5rem 3rem;
    background: var(--fintech-gradient-primary);
    color: var(--fintech-dark);
    text-decoration: none;
    border-radius: 50px;
    font-weight: 700;
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.4s ease;
    font-size: 1.2rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 15px 40px rgba(255, 215, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.cta-section .button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.cta-section .button:hover::before {
    left: 100%;
}

.cta-section .button:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 50px rgba(255, 215, 0, 0.4);
}

/* Footer Styles */
.footer {
    background: var(--fintech-gradient-dark);
    color: var(--fintech-text-light);
    padding: 4rem 0 2rem;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(255, 215, 0, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 165, 0, 0.03) 0%, transparent 50%);
    z-index: 0;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
    position: relative;
    z-index: 1;
}

.footer-section h4 {
    color: var(--fintech-primary);
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.footer-section ul {
    list-style: none;
    padding: 0;
}

.footer-section ul li {
    margin-bottom: 0.75rem;
}

.footer-section ul li a {
    color: var(--fintech-text-muted);
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.footer-section ul li a:hover {
    color: var(--fintech-primary);
    padding-left: 0.5rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.footer-logo img {
    width: 50px;
    height: 50px;
}

.logo-text {
    display: flex;
    flex-direction: column;
}

.logo-primary {
    font-size: 1.5rem;
    font-weight: 900;
    color: var(--asimov-blue);
    letter-spacing: 2px;
}

.logo-secondary {
    font-size: 0.9rem;
    color: var(--fintech-text-muted);
    font-weight: 600;
    letter-spacing: 1px;
}

.footer-section p {
    color: var(--fintech-text-muted);
    line-height: 1.6;
    font-size: 1.1rem;
}

.footer-contact li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--fintech-text-muted);
    font-weight: 500;
}

.footer-contact i {
    color: var(--fintech-primary);
    width: 20px;
    text-align: center;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 215, 0, 0.2);
    padding-top: 2rem;
    text-align: center;
    position: relative;
    z-index: 1;
}

.footer-copyright {
    color: var(--fintech-text-muted);
    font-size: 0.9rem;
    font-weight: 500;
}

/* Responsividade */
@media (max-width: 1200px) {
    .hero-pricing {
        max-width: 900px;
        margin: 0 auto;
        padding: 3rem;
    }

    .pricing-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }

    .fintech-hero .hero-title {
        font-size: 3.5rem;
    }

    .product-hero-header .section-title {
        font-size: 3rem;
    }
}

@media (max-width: 900px) {
    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .pricing-card.featured {
        transform: none;
        order: -1;
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
}

@media (max-width: 768px) {
    .fintech-hero {
        padding: 120px 0 80px;
        min-height: auto;
    }

    .fintech-hero .hero-title {
        font-size: 2.8rem;
    }

    .fintech-hero .hero-description {
        font-size: 1.2rem;
        margin-bottom: 3rem;
    }

    .hero-stats {
        grid-template-columns: 1fr;
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .stat-item {
        padding: 1.5rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .feature-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .feature-item {
        padding: 2rem;
    }

    .fintech-hero .hero-cta {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .fintech-hero .button {
        padding: 1rem 2rem;
        font-size: 1rem;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .product-card {
        padding: 2rem;
    }

    .fintech-products .section-title {
        font-size: 2.5rem;
    }

    .product-hero-header .section-title {
        font-size: 2.5rem;
    }

    .cta-content h2 {
        font-size: 2.5rem;
    }

    .cta-content p {
        font-size: 1.2rem;
    }

    .cta-section .button {
        padding: 1.2rem 2.5rem;
        font-size: 1rem;
    }

    .hero-pricing {
        padding: 2rem;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .pricing-card {
        padding: 2rem;
    }

    .pricing-card.featured {
        transform: none;
        margin: 1rem 0;
    }

    .pricing-card.featured::before {
        top: -10px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .fintech-hero .hero-title {
        font-size: 2.2rem;
    }

    .fintech-hero .hero-badge {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }

    .hero-stats {
        gap: 1.5rem;
    }

    .stat-item {
        padding: 1rem;
    }

    .feature-item {
        padding: 1.5rem;
    }

    .product-card {
        padding: 1.5rem;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .product-hero-header .section-title {
        font-size: 2rem;
    }

    .fintech-products .section-title {
        font-size: 2rem;
    }
}
