/* ===== ASIMOV IOT & SMARTCITY STYLES - BLUE/GREEN THEME ===== */

/* Override ASIMOV logo colors to use blue theme */
.navbar .logo-primary,
.footer .logo-primary {
    color: var(--asimov-blue) !important;
}

/* Force center alignment for section titles - Override style.css conflicts */
#enviromesh .section-title,
.iot-products .section-title,
.product-hero-header .section-title,
.pricing-section .section-title,
.products-grid-section .section-title,
.section-title {
    text-align: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
    display: block !important;
    width: 100% !important;
    left: auto !important;
    transform: none !important;
    position: relative !important;
}

/* Ensure containers are also centered */
.product-hero-header,
.iot-products .section-header,
.pricing-section .section-header,
.products-grid-section .section-header,
.section-header {
    text-align: center !important;
    width: 100% !important;
    display: block !important;
}

:root {
    /* IoT Color Palette - Blue & Green Theme */
    --iot-primary: #0f4c75;                 /* Primary Dark Blue */
    --iot-secondary: #3282b8;               /* Secondary Blue */
    --iot-tertiary: #bbe1fa;                /* Light Blue */
    --iot-accent: #00ff88;                  /* Green Accent */
    --iot-accent-blue: #00d4ff;             /* Light Blue Accent */
    --iot-light: #f8fafc;                   /* Light Background */
    --iot-dark: #1a202c;                    /* Dark Text */
    --iot-text-light: #F5F5F5;              /* Light Text */
    --iot-text-muted: #CCCCCC;              /* Muted Text */

    /* ASIMOV Brand Colors - Blue Theme */
    --asimov-blue: #2D7FF9;                 /* Primary Blue for ASIMOV logo */
    --asimov-blue-dark: #1C57B5;            /* Darker Blue */

    /* Gradients */
    --iot-gradient-primary: linear-gradient(135deg, #0f4c75 0%, #3282b8 50%, #bbe1fa 100%);
    --iot-gradient-light: linear-gradient(135deg, #f8fafc 0%, #e6f0ff 100%);
    --iot-gradient-accent: linear-gradient(135deg, #00ff88 0%, #00d4ff 100%);
}

/* Mobile menu styles for iot page */
.mobile-menu {
    position: fixed;
    top: 80px;
    left: 0;
    right: 0;
    background: white;
    padding: 1rem;
    border-bottom: 1px solid var(--neutral-200);
    transform: translateY(-100%);
    transition: transform 0.3s ease;
    z-index: 999;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.mobile-menu.active {
    transform: translateY(0);
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    color: var(--neutral-700);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.mobile-nav-link:hover {
    background: var(--primary-light);
    color: var(--primary-color);
}

.mobile-nav-link i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.mobile-menu-button {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
}

.mobile-menu-button span {
    width: 25px;
    height: 3px;
    background: var(--neutral-700);
    border-radius: 2px;
    transition: all 0.3s ease;
}

.mobile-menu-button.active span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-button.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-button.active span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* IoT Hero Section Enhancements */
.iot-hero {
    background: var(--iot-gradient-primary);
    position: relative;
    overflow: hidden;
}

.iot-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="iot-grid" width="15" height="15" patternUnits="userSpaceOnUse"><circle cx="7.5" cy="7.5" r="1.5" fill="rgba(255,255,255,0.2)"/><path d="M 0 7.5 L 15 7.5 M 7.5 0 L 7.5 15" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23iot-grid)"/></svg>');
    opacity: 0.3;
}

.iot-hero .hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: 3rem;
    max-width: 600px;
    opacity: 1;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    color: white;
    font-weight: 400;
}

/* Product Hero Section */
.product-hero {
    background: var(--iot-light);
    padding: 6rem 0;
}

.product-hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

/* IoT Dashboard Enhancements */
.iot-dashboard {
    padding: 2rem;
    background: white;
    border-radius: 16px;
    border: 1px solid var(--iot-secondary);
    box-shadow: 0 10px 30px rgba(15, 76, 117, 0.1);
}

.sensor-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.sensor-card {
    background: var(--iot-light);
    padding: 1rem;
    border-radius: 12px;
    border: 1px solid var(--neutral-200);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    position: relative;
}

.sensor-icon {
    width: 40px;
    height: 40px;
    background: var(--iot-gradient-primary);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.sensor-data {
    flex: 1;
}

.sensor-value {
    display: block;
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--iot-primary);
}

.sensor-label {
    display: block;
    font-size: 0.8rem;
    color: var(--neutral-600);
}

.sensor-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
}

.sensor-status.good {
    background: var(--iot-accent);
}

.sensor-status.warning {
    background: #ffa500;
}

.map-preview {
    background: var(--iot-light);
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    border: 1px solid var(--neutral-200);
}

.map-placeholder {
    color: var(--iot-secondary);
    font-size: 1.2rem;
}

.map-placeholder i {
    display: block;
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

/* Pricing Section Enhancements */
.pricing-section {
    background: var(--iot-gradient-light);
    padding: 6rem 0;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 3rem;
}

.pricing-card {
    background: white;
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 10px 30px rgba(15, 76, 117, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 255, 136, 0.2);
}

.pricing-card.featured {
    border: 2px solid var(--iot-accent);
    transform: scale(1.05);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.pricing-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--iot-gradient-accent);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Products Grid Section */
.products-grid-section {
    background: white;
    padding: 6rem 0;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.product-card {
    background: white;
    border: 1px solid var(--neutral-200);
    border-radius: 16px;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 255, 136, 0.15);
    border-color: var(--iot-accent);
}

.product-icon {
    width: 60px;
    height: 60px;
    background: var(--iot-gradient-primary);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.product-icon i {
    font-size: 1.5rem;
    color: white;
}

/* Status badges */
.status-badge.active {
    background: #dcfce7;
    color: #166534;
}

.status-badge.beta {
    background: #dbeafe;
    color: #1e40af;
}

/* CTA Section */
.cta-section {
    background: var(--iot-gradient-primary);
    color: white;
    padding: 6rem 0;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Button Styles */
.button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    text-align: center;
    justify-content: center;
}

.button-primary {
    background: var(--iot-accent);
    color: var(--iot-primary);
    box-shadow: 0 4px 15px rgba(255, 165, 0, 0.3);
}

.button-primary:hover {
    background: var(--iot-accent-blue);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 140, 0, 0.4);
}

.button-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.button-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.button-outline {
    background: transparent;
    color: var(--iot-accent);
    border: 2px solid var(--iot-accent);
}

.button-outline:hover {
    background: var(--iot-accent);
    color: var(--iot-primary);
    border-color: var(--iot-accent);
}

.button i {
    font-size: 1.1rem;
}

/* Pricing Section Button Overrides */
.pricing-card .button-outline {
    background: transparent;
    color: var(--iot-accent);
    border: 2px solid var(--iot-accent);
}

.pricing-card .button-outline:hover {
    background: var(--iot-accent);
    color: var(--iot-primary);
    border-color: var(--iot-accent);
}

.pricing-card .button-primary {
    background: var(--iot-accent);
    color: var(--iot-primary);
    box-shadow: 0 4px 15px rgba(255, 165, 0, 0.3);
}

.pricing-card .button-primary:hover {
    background: var(--iot-accent-blue);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 140, 0, 0.4);
}

/* Responsividade */
@media (max-width: 1200px) {
    .pricing-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }

    .product-hero-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
}

@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .mobile-menu-button {
        display: flex;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .iot-hero {
        padding: 6rem 0 4rem;
    }

    .iot-hero .hero-title {
        font-size: 2.5rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .sensor-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .iot-dashboard {
        padding: 1rem;
    }

    .map-preview {
        padding: 1.5rem;
    }
}
