/* ===== ASIMOV LEGAL & CONTÁBIL STYLES - DARK BLUE/GOLD THEME ===== */

/* Force center alignment for section titles */
.legal-products .section-title,
.product-hero-header .section-title,
.pricing-section .section-title,
.products-grid-section .section-title,
.section-title {
    text-align: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
    display: block !important;
    width: 100% !important;
    left: auto !important;
    transform: none !important;
    position: relative !important;
}

/* Ensure containers are also centered */
.product-hero-header,
.legal-products .section-header,
.pricing-section .section-header,
.products-grid-section .section-header,
.section-header {
    text-align: center !important;
    width: 100% !important;
    display: block !important;
}

:root {
    /* Legal Color Palette - Dark Blue & Gold Theme */
    --legal-primary: #1a237e;              /* Deep Blue */
    --legal-secondary: #283593;            /* Medium Blue */
    --legal-tertiary: #3949ab;             /* Light Blue */
    --legal-accent: #ffd700;               /* Gold Accent */
    --legal-accent-dark: #ffb300;          /* Dark Gold */
    --legal-light: #f8f9fa;                /* Light Background */
    --legal-dark: #1a202c;                 /* Dark Text */
    --legal-text-light: #F5F5F5;           /* Light Text */
    --legal-text-muted: #CCCCCC;           /* Muted Text */

    /* ASIMOV Brand Colors - Legal Theme */
    --asimov-legal: #1a237e;               /* Primary Blue for ASIMOV logo */
    --asimov-legal-dark: #0d47a1;          /* Darker Blue */

    /* Gradients */
    --legal-gradient-primary: linear-gradient(135deg, #1a237e 0%, #283593 50%, #3949ab 100%);
    --legal-gradient-light: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
    --legal-gradient-accent: linear-gradient(135deg, #ffd700 0%, #ffb300 100%);
}



/* Legal Hero Section */
.legal-hero {
    background: var(--legal-gradient-primary);
    position: relative;
    overflow: hidden;
    padding: 8rem 0 6rem;
    color: white;
    text-align: center;
}

.legal-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="legal-grid" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,215,0,0.2)"/><path d="M 0 10 L 20 10 M 10 0 L 10 20" stroke="rgba(255,215,0,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23legal-grid)"/></svg>');
    opacity: 0.3;
}

.legal-hero .hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
}

.legal-hero .hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 215, 0, 0.2);
    border: 1px solid rgba(255, 215, 0, 0.3);
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 2rem;
    backdrop-filter: blur(10px);
}

.legal-hero .hero-badge i {
    color: var(--legal-accent);
}

.legal-hero .hero-title {
    font-size: 3.5rem;
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #e3f2fd 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.legal-hero .hero-title .highlight {
    background: var(--legal-gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.legal-hero .hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: 3rem;
    max-width: 600px;
    opacity: 1;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    color: white;
    font-weight: 400;
}

.legal-hero .hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.legal-hero .stat-item {
    text-align: center;
}

.legal-hero .stat-number {
    font-size: 2.5rem;
    font-weight: 900;
    color: var(--legal-accent);
    margin-bottom: 0.5rem;
}

.legal-hero .stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
    font-weight: 500;
}

.legal-hero .hero-cta {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Button Styles */
.button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    text-align: center;
    justify-content: center;
}

.button-primary {
    background: var(--legal-accent);
    color: var(--legal-primary);
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.button-primary:hover {
    background: var(--legal-accent-dark);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
}

.button-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.button-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.button-outline {
    background: transparent;
    color: var(--legal-accent);
    border: 2px solid var(--legal-accent);
}

.button-outline:hover {
    background: var(--legal-accent);
    color: var(--legal-primary);
    border-color: var(--legal-accent);
}

.button i {
    font-size: 1.1rem;
}

/* Solution Section */
.solution-section {
    padding: 6rem 0;
    background: var(--legal-light);
}

.solution-section .section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.solution-section .section-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--legal-primary);
    margin-bottom: 1rem;
}

.solution-section .section-title .highlight {
    background: var(--legal-gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.solution-section .section-subtitle {
    font-size: 1.2rem;
    color: var(--legal-dark);
    opacity: 0.8;
}

.solution-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.solution-card {
    background: white;
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(26, 35, 126, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(26, 35, 126, 0.1);
}

.solution-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(26, 35, 126, 0.15);
}

.solution-icon {
    width: 80px;
    height: 80px;
    background: var(--legal-gradient-primary);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.solution-icon i {
    font-size: 2rem;
    color: white;
}

.solution-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--legal-primary);
    margin-bottom: 1rem;
}

.solution-card p {
    color: var(--legal-dark);
    opacity: 0.8;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.solution-features {
    list-style: none;
    padding: 0;
}

.solution-features li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    color: var(--legal-dark);
}

.solution-features i {
    color: var(--legal-accent);
    font-size: 0.9rem;
}

/* Tech Stack Section */
.tech-stack-section {
    padding: 6rem 0;
    background: var(--legal-gradient-primary);
    color: white;
}

.tech-stack-section .section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.tech-stack-section .section-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
}

.tech-stack-section .section-title .highlight {
    background: var(--legal-gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.tech-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.tech-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-5px);
}

.tech-icon {
    width: 60px;
    height: 60px;
    background: var(--legal-gradient-accent);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.tech-icon i {
    font-size: 1.5rem;
    color: var(--legal-primary);
}

.tech-item h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.tech-item p {
    opacity: 0.9;
    font-size: 0.9rem;
}

/* Use Cases Section */
.use-cases-section {
    padding: 6rem 0;
    background: var(--legal-light);
}

.use-cases-section .section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.use-cases-section .section-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--legal-primary);
    margin-bottom: 1rem;
}

.use-cases-section .section-title .highlight {
    background: var(--legal-gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.use-cases-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
    margin-top: 3rem;
}

.use-case-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(26, 35, 126, 0.1);
    transition: all 0.3s ease;
}

.use-case-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(26, 35, 126, 0.15);
}

.use-case-header {
    background: var(--legal-gradient-primary);
    padding: 2rem;
    text-align: center;
    color: white;
}

.use-case-header i {
    font-size: 3rem;
    color: var(--legal-accent);
    margin-bottom: 1rem;
}

.use-case-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.use-case-content {
    padding: 2rem;
}

.use-case-list {
    list-style: none;
    padding: 0;
}

.use-case-list li {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    color: var(--legal-dark);
    font-weight: 500;
}

.use-case-list i {
    color: var(--legal-accent);
    font-size: 0.9rem;
    flex-shrink: 0;
}

/* Pricing Section */
.pricing-section {
    padding: 6rem 0;
    background: white;
}

.pricing-section .section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.pricing-section .section-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--legal-primary);
    margin-bottom: 1rem;
}

.pricing-section .section-title .highlight {
    background: var(--legal-gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.pricing-card {
    background: white;
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 10px 30px rgba(26, 35, 126, 0.1);
    border: 2px solid transparent;
    transition: all 0.3s ease;
    position: relative;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(26, 35, 126, 0.15);
}

.pricing-card.featured {
    border-color: var(--legal-accent);
    transform: scale(1.05);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.pricing-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--legal-gradient-accent);
    color: var(--legal-primary);
    padding: 0.5rem 1.5rem;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
}

.pricing-header {
    text-align: center;
    margin-bottom: 2rem;
}

.plan-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--legal-primary);
    margin-bottom: 1rem;
}

.plan-price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 0.25rem;
    margin-bottom: 1rem;
}

.currency {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--legal-accent);
}

.amount {
    font-size: 3rem;
    font-weight: 900;
    color: var(--legal-primary);
}

.period {
    font-size: 1rem;
    color: var(--legal-dark);
    opacity: 0.7;
}

.plan-description {
    color: var(--legal-dark);
    opacity: 0.8;
}

.pricing-features {
    margin-bottom: 2rem;
}

.pricing-features .feature {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    color: var(--legal-dark);
}

.pricing-features .feature i {
    color: var(--legal-accent);
    font-size: 0.9rem;
}

/* Pricing Section Button Overrides */
.pricing-card .button-outline {
    background: transparent;
    color: var(--legal-accent);
    border: 2px solid var(--legal-accent);
}

.pricing-card .button-outline:hover {
    background: var(--legal-accent);
    color: var(--legal-primary);
    border-color: var(--legal-accent);
}

.pricing-card .button-primary {
    background: var(--legal-accent);
    color: var(--legal-primary);
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.pricing-card .button-primary:hover {
    background: var(--legal-accent-dark);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
}

/* Case Study Section */
.case-study-section {
    padding: 6rem 0;
    background: var(--legal-gradient-primary);
    color: white;
    text-align: center;
}

.case-study-content {
    max-width: 800px;
    margin: 0 auto;
}

.case-study-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 215, 0, 0.2);
    border: 1px solid rgba(255, 215, 0, 0.3);
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 2rem;
    backdrop-filter: blur(10px);
}

.case-study-badge i {
    color: var(--legal-accent);
}

.case-study-section h2 {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
}

.case-study-description {
    font-size: 1.2rem;
    line-height: 1.6;
    opacity: 0.9;
    margin-bottom: 3rem;
}

.case-study-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.case-study-metrics .metric {
    text-align: center;
}

.metric-number {
    font-size: 2.5rem;
    font-weight: 900;
    color: var(--legal-accent);
    margin-bottom: 0.5rem;
}

.metric-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* CTA Section */
.cta-section {
    padding: 6rem 0;
    background: var(--legal-light);
    text-align: center;
}

.cta-content {
    max-width: 600px;
    margin: 0 auto;
}

.cta-section h2 {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--legal-primary);
    margin-bottom: 1.5rem;
}

.cta-section p {
    font-size: 1.2rem;
    color: var(--legal-dark);
    opacity: 0.8;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Responsividade */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .mobile-menu-button {
        display: flex;
    }

    .legal-hero {
        padding: 6rem 0 4rem;
    }

    .legal-hero .hero-title {
        font-size: 2.5rem;
    }

    .legal-hero .hero-cta {
        flex-direction: column;
        align-items: center;
    }

    .legal-hero .hero-stats {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }

    .solution-grid,
    .tech-grid {
        grid-template-columns: 1fr;
    }

    .use-cases-grid {
        grid-template-columns: 1fr;
    }

    .solution-section .section-title,
    .tech-stack-section .section-title,
    .use-cases-section .section-title,
    .pricing-section .section-title,
    .case-study-section h2,
    .cta-section h2 {
        font-size: 2rem;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
    }

    .pricing-card.featured {
        transform: none;
    }

    .pricing-card.featured:hover {
        transform: translateY(-10px);
    }

    .case-study-metrics {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
}
